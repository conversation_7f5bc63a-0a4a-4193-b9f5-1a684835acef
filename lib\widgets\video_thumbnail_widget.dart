import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:video_player/video_player.dart';


/// Widget لعرض معاينة الفيديو مع thumbnail مثل واتساب
class VideoPreviewWidget extends StatefulWidget {
  final String videoPath;
  final VoidCallback? onTap;
  final double width;
  final double height;
  final bool isMyMessage;
  final bool showPlayButton;

  const VideoPreviewWidget({
    super.key,
    required this.videoPath,
    this.onTap,
    this.width = 250,
    this.height = 180,
    this.isMyMessage = false,
    this.showPlayButton = true,
  });

  @override
  State<VideoPreviewWidget> createState() => _VideoPreviewWidgetState();
}

class _VideoPreviewWidgetState extends State<VideoPreviewWidget> {
  Uint8List? _thumbnailData;
  bool _isLoading = true;
  String? _error;
  String? _duration;

  @override
  void initState() {
    super.initState();
    _generateThumbnail();
  }

  Future<void> _generateThumbnail() async {
    try {
      debugPrint('🎬 بدء إنشاء thumbnail للفيديو: ${widget.videoPath}');

      // التحقق من وجود الملف
      final file = File(widget.videoPath);
      if (!await file.exists()) {
        setState(() {
          _error = 'الملف غير موجود';
          _isLoading = false;
        });
        return;
      }

      // قراءة مدة الفيديو الحقيقية
      await _getVideoDuration();

      // إنشاء thumbnail
      final thumbnailData = await VideoThumbnail.thumbnailData(
        video: widget.videoPath,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 300,
        maxHeight: 200,
        quality: 75,
      );

      if (thumbnailData != null) {
        debugPrint('✅ تم إنشاء thumbnail بنجاح (${thumbnailData.length} بايت)');
        setState(() {
          _thumbnailData = thumbnailData;
          _isLoading = false;
        });
      } else {
        debugPrint('❌ فشل في إنشاء thumbnail');
        setState(() {
          _error = 'فشل في إنشاء المعاينة';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء thumbnail: $e');
      setState(() {
        _error = 'خطأ في المعاينة';
        _isLoading = false;
      });
    }
  }

  Future<void> _getVideoDuration() async {
    try {
      debugPrint('⏱️ بدء قراءة مدة الفيديو: ${widget.videoPath}');

      final file = File(widget.videoPath);
      if (!await file.exists()) {
        debugPrint('❌ ملف الفيديو غير موجود: ${widget.videoPath}');
        setState(() {
          _duration = '0:00';
        });
        return;
      }

      final controller = VideoPlayerController.file(file);

      // إعطاء وقت أكثر للتهيئة
      await controller.initialize().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('⏰ انتهت مهلة تهيئة الفيديو');
          throw Exception('Video initialization timeout');
        },
      );

      final duration = controller.value.duration;

      if (duration == Duration.zero) {
        debugPrint('⚠️ مدة الفيديو صفر، محاولة قراءة البيانات مرة أخرى...');
        // انتظار قصير ثم محاولة مرة أخرى
        await Future.delayed(const Duration(milliseconds: 500));
        final newDuration = controller.value.duration;

        if (newDuration != Duration.zero) {
          final minutes = newDuration.inMinutes;
          final seconds = newDuration.inSeconds % 60;
          setState(() {
            _duration = '$minutes:${seconds.toString().padLeft(2, '0')}';
          });
          debugPrint('✅ مدة الفيديو (المحاولة الثانية): $_duration');
        } else {
          debugPrint('❌ لا يمكن قراءة مدة الفيديو');
          setState(() {
            _duration = '0:00';
          });
        }
      } else {
        final minutes = duration.inMinutes;
        final seconds = duration.inSeconds % 60;
        setState(() {
          _duration = '$minutes:${seconds.toString().padLeft(2, '0')}';
        });
        debugPrint('✅ مدة الفيديو: $_duration');
      }

      await controller.dispose();
    } catch (e) {
      debugPrint('❌ خطأ في قراءة مدة الفيديو: $e');
      setState(() {
        _duration = '0:00';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: widget.width,
          height: widget.height,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // خلفية الفيديو
              _buildVideoBackground(),

              // زر التشغيل الشفاف (إذا مطلوب)
              if (widget.showPlayButton) _buildPlayButton(),

              // مدة الفيديو في الزاوية
              Positioned(
                bottom: 6,
                right: 6,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _duration ?? '0:00',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoBackground() {
    if (_isLoading) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
          ),
        ),
      );
    }

    if (_error != null) {
      return Container(
        color: Colors.grey[800],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white70,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              _error!,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_thumbnailData != null) {
      return Image.memory(
        _thumbnailData!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Icon(
              Icons.video_file,
              color: Colors.white70,
              size: 48,
            ),
          );
        },
      );
    }

    return Container(
      color: Colors.grey[800],
      child: const Icon(
        Icons.video_file,
        color: Colors.white70,
        size: 48,
      ),
    );
  }

  Widget _buildPlayButton() {
    return Center(
      child: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.play_arrow,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }
}
