import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';

/// خدمة مشاركة المحادثات
class ChatShareService {
  
  /// مشاركة رسائل محددة كنص
  static Future<void> shareMessages(
    List<ChatMessage> messages, 
    String chatName,
    BuildContext context,
  ) async {
    try {
      if (messages.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ لا توجد رسائل للمشاركة'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
      final buffer = StringBuffer();
      
      // عنوان المحادثة
      buffer.writeln('💬 محادثة واتساب: $chatName');
      buffer.writeln('📅 تم التصدير: ${dateFormat.format(DateTime.now())}');
      buffer.writeln('📊 عدد الرسائل: ${messages.length}');
      buffer.writeln('${'=' * 50}');
      buffer.writeln();
      
      // الرسائل
      for (final message in messages) {
        final time = dateFormat.format(message.timestamp);
        
        switch (message.type) {
          case 'text':
            buffer.writeln('[$time] ${message.sender}: ${message.content}');
            break;
          case 'image':
            buffer.writeln('[$time] ${message.sender}: 📷 صورة - ${message.content}');
            break;
          case 'video':
            buffer.writeln('[$time] ${message.sender}: 🎥 فيديو - ${message.content}');
            break;
          case 'audio':
            buffer.writeln('[$time] ${message.sender}: 🎧 رسالة صوتية - ${message.content}');
            break;
          case 'document':
            buffer.writeln('[$time] ${message.sender}: 📄 مستند - ${message.content}');
            break;
          case 'call':
          case 'unknown_call':
            buffer.writeln('[$time] ${message.sender}: 📞 مكالمة');
            break;
          default:
            buffer.writeln('[$time] ${message.sender}: ${message.content}');
        }
      }
      
      buffer.writeln();
      buffer.writeln('${'=' * 50}');
      buffer.writeln('تم إنشاء هذا الملف بواسطة عارض واتساب');
      
      await Share.share(
        buffer.toString(),
        subject: 'محادثة واتساب: $chatName',
      );
      
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة الرسائل: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ خطأ في المشاركة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// مشاركة المحادثة كملف نصي مع اختيار التاريخ
  static Future<void> shareAsTextFile(
    List<ChatMessage> messages,
    String chatName,
    BuildContext context,
  ) async {
    try {
      if (messages.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ لا توجد رسائل للمشاركة'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // عرض نافذة اختيار التاريخ
      final dateRange = await _showDateRangeDialog(context, messages);
      if (dateRange == null) return; // المستخدم ألغى العملية

      // تصفية الرسائل حسب التاريخ
      final filteredMessages = messages.where((message) {
        return message.timestamp.isAfter(dateRange.start.subtract(const Duration(days: 1))) &&
               message.timestamp.isBefore(dateRange.end.add(const Duration(days: 1)));
      }).toList();

      if (filteredMessages.isEmpty) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ لا توجد رسائل في الفترة المحددة'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // إنشاء محتوى الملف
      final content = _generateTextContent(filteredMessages, chatName, dateRange);

      // عرض معاينة النص
      if (!context.mounted) return;
      final shouldShare = await _showPreviewDialog(context, content, filteredMessages.length);
      if (!shouldShare) return; // المستخدم ألغى العملية

      // حفظ كملف
      final directory = await getTemporaryDirectory();
      final fileName = 'محادثة_${chatName.replaceAll(RegExp(r'[^\w\s-]'), '_')}_${DateFormat('yyyyMMdd').format(dateRange.start)}_${DateFormat('yyyyMMdd').format(dateRange.end)}.txt';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(content);

      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'محادثة واتساب: $chatName (${DateFormat('dd/MM/yyyy').format(dateRange.start)} - ${DateFormat('dd/MM/yyyy').format(dateRange.end)})',
        text: 'محادثة واتساب مُصدرة من عارض واتساب',
      );

    } catch (e) {
      debugPrint('❌ خطأ في مشاركة الملف: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في المشاركة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض نافذة اختيار نطاق التاريخ
  static Future<DateTimeRange?> _showDateRangeDialog(BuildContext context, List<ChatMessage> messages) async {
    final firstDate = messages.first.timestamp;
    final lastDate = messages.last.timestamp;

    return await showDateRangePicker(
      context: context,
      firstDate: firstDate,
      lastDate: lastDate,
      initialDateRange: DateTimeRange(start: firstDate, end: lastDate),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF25D366),
              onPrimary: Colors.white,
              surface: Color(0xFF2A2F32),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
      helpText: 'اختر نطاق التاريخ للتصدير',
      cancelText: 'إلغاء',
      confirmText: 'موافق',
      saveText: 'حفظ',
      errorFormatText: 'تنسيق تاريخ غير صحيح',
      errorInvalidText: 'تاريخ غير صالح',
      errorInvalidRangeText: 'نطاق تاريخ غير صالح',
      fieldStartHintText: 'تاريخ البداية',
      fieldEndHintText: 'تاريخ النهاية',
      fieldStartLabelText: 'من تاريخ',
      fieldEndLabelText: 'إلى تاريخ',
    );
  }

  /// عرض نافذة معاينة النص
  static Future<bool> _showPreviewDialog(BuildContext context, String content, int messageCount) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📄 معاينة الملف النصي'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.info, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text('📊 عدد الرسائل: $messageCount'),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      content.length > 2000 ? '${content.substring(0, 2000)}\n\n... (تم اقتطاع النص للمعاينة)' : content,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('مشاركة'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// إنشاء محتوى الملف النصي
  static String _generateTextContent(List<ChatMessage> messages, String chatName, DateTimeRange dateRange) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final buffer = StringBuffer();

    // عنوان المحادثة
    buffer.writeln('💬 محادثة واتساب: $chatName');
    buffer.writeln('📅 تم التصدير: ${dateFormat.format(DateTime.now())}');
    buffer.writeln('📊 عدد الرسائل: ${messages.length}');
    buffer.writeln('🗓️ من تاريخ: ${DateFormat('dd/MM/yyyy').format(dateRange.start)}');
    buffer.writeln('🗓️ إلى تاريخ: ${DateFormat('dd/MM/yyyy').format(dateRange.end)}');
    buffer.writeln('${'=' * 50}');
    buffer.writeln();

    // الرسائل
    for (final message in messages) {
      final time = dateFormat.format(message.timestamp);

      switch (message.type) {
        case 'text':
          buffer.writeln('[$time] ${message.sender}: ${message.content}');
          break;
        case 'image':
          buffer.writeln('[$time] ${message.sender}: 📷 صورة - ${message.content}');
          break;
        case 'video':
          buffer.writeln('[$time] ${message.sender}: 🎥 فيديو - ${message.content}');
          break;
        case 'audio':
          buffer.writeln('[$time] ${message.sender}: 🎧 رسالة صوتية - ${message.content}');
          break;
        case 'document':
          buffer.writeln('[$time] ${message.sender}: 📄 مستند - ${message.content}');
          break;
        case 'call':
        case 'unknown_call':
          buffer.writeln('[$time] ${message.sender}: 📞 مكالمة');
          break;
        case 'emoji':
          buffer.writeln('[$time] ${message.sender}: ${message.content}');
          break;
        case 'deleted':
          buffer.writeln('[$time] ${message.sender}: 🗑️ رسالة محذوفة');
          break;
        default:
          buffer.writeln('[$time] ${message.sender}: ${message.content}');
      }
    }

    buffer.writeln();
    buffer.writeln('${'=' * 50}');
    buffer.writeln('تم إنشاء هذا الملف بواسطة عارض واتساب v2.0.0');

    return buffer.toString();
  }
  
  /// عرض خيارات المشاركة
  static Future<void> showShareOptions(
    List<ChatMessage> messages, 
    String chatName,
    BuildContext context,
  ) async {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '📤 مشاركة المحادثة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.text_fields, color: Colors.blue),
              title: const Text('مشاركة كنص'),
              subtitle: const Text('مشاركة الرسائل كنص عادي'),
              onTap: () {
                Navigator.of(context).pop();
                shareMessages(messages, chatName, context);
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.description, color: Colors.green),
              title: const Text('مشاركة كملف نصي'),
              subtitle: const Text('حفظ ومشاركة كملف .txt'),
              onTap: () {
                Navigator.of(context).pop();
                shareAsTextFile(messages, chatName, context);
              },
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      ),
    );
  }
  
  /// مشاركة إحصائيات المحادثة
  static Future<void> shareChatStatistics(
    List<ChatMessage> messages, 
    String chatName,
    BuildContext context,
  ) async {
    try {
      if (messages.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ لا توجد رسائل لحساب الإحصائيات'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // حساب الإحصائيات
      final senderCounts = <String, int>{};
      final typeCounts = <String, int>{};
      
      for (final message in messages) {
        senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
        typeCounts[message.type] = (typeCounts[message.type] ?? 0) + 1;
      }
      
      final sortedSenders = senderCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      final buffer = StringBuffer();
      buffer.writeln('📊 إحصائيات محادثة: $chatName');
      buffer.writeln('📅 تاريخ التقرير: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
      buffer.writeln('${'=' * 50}');
      buffer.writeln();
      
      buffer.writeln('📈 إجمالي الرسائل: ${messages.length}');
      buffer.writeln('👥 عدد المشاركين: ${senderCounts.length}');
      buffer.writeln();
      
      buffer.writeln('🏆 أكثر المرسلين نشاطاً:');
      for (int i = 0; i < sortedSenders.length && i < 5; i++) {
        final entry = sortedSenders[i];
        final percentage = (entry.value / messages.length * 100).toStringAsFixed(1);
        buffer.writeln('${i + 1}. ${entry.key}: ${entry.value} رسالة ($percentage%)');
      }
      buffer.writeln();
      
      buffer.writeln('📝 أنواع الرسائل:');
      typeCounts.forEach((type, count) {
        String typeName = '';
        switch (type) {
          case 'text':
            typeName = '💬 نصية';
            break;
          case 'image':
            typeName = '📷 صور';
            break;
          case 'video':
            typeName = '🎥 فيديو';
            break;
          case 'audio':
            typeName = '🎧 صوتية';
            break;
          case 'document':
            typeName = '📄 مستندات';
            break;
          case 'call':
          case 'unknown_call':
            typeName = '📞 مكالمات';
            break;
          default:
            typeName = '❓ أخرى';
        }
        final percentage = (count / messages.length * 100).toStringAsFixed(1);
        buffer.writeln('$typeName: $count ($percentage%)');
      });
      
      buffer.writeln();
      buffer.writeln('${'=' * 50}');
      buffer.writeln('تم إنشاء هذا التقرير بواسطة عارض واتساب');
      
      await Share.share(
        buffer.toString(),
        subject: 'إحصائيات محادثة: $chatName',
      );
      
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة الإحصائيات: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في المشاركة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
