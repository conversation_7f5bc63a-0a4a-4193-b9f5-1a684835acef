#!/usr/bin/env python3
"""
إنشاء أيقونة Mona الجميلة مع قلب ملون
"""

from PIL import Image, ImageDraw, ImageFont
import math

def create_mona_heart_icon():
    # إنشاء صورة مربعة عالية الجودة
    size = 1024  # حجم كبير للجودة العالية
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان جميلة لأيقونة Mona
    bg_color = (255, 20, 147, 255)  # Deep Pink خلفية
    heart_color = (255, 105, 180, 255)  # Hot Pink للقلب
    accent_color = (135, 206, 250, 255)  # Light Sky Blue للتفاصيل
    text_color = (255, 255, 255, 255)  # أبيض للنص
    
    # رسم خلفية دائرية ملونة
    margin = size // 10
    circle_size = size - (margin * 2)
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                 fill=bg_color)
    
    # رسم قلب كبير في المنتصف
    center_x, center_y = size // 2, size // 2 - size // 8
    heart_size = size // 4
    
    # رسم القلب بطريقة رياضية دقيقة
    def draw_heart(cx, cy, size, color):
        points = []
        for i in range(360):
            t = math.radians(i)
            # معادلة القلب الرياضية
            x = 16 * math.sin(t)**3
            y = -(13 * math.cos(t) - 5 * math.cos(2*t) - 2 * math.cos(3*t) - math.cos(4*t))
            
            # تحويل إلى إحداثيات الشاشة
            px = cx + (x * size // 20)
            py = cy + (y * size // 20)
            points.append((px, py))
        
        draw.polygon(points, fill=color)
    
    # رسم القلب الرئيسي
    draw_heart(center_x, center_y, heart_size, heart_color)
    
    # رسم قلب أصغر بلون مختلف في المنتصف
    draw_heart(center_x, center_y, heart_size // 2, accent_color)
    
    # إضافة نص "Mona" بخط كبير
    try:
        # محاولة استخدام خط جميل
        font_size = size // 12
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي
        font = ImageFont.load_default()
    
    text = "Mona"
    # حساب موقع النص
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (size - text_width) // 2
    text_y = center_y + heart_size + size // 15
    
    # رسم ظل للنص
    shadow_offset = 3
    draw.text((text_x + shadow_offset, text_y + shadow_offset), text, 
              fill=(0, 0, 0, 128), font=font)
    
    # رسم النص الرئيسي
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    # إضافة نجوم صغيرة حول القلب
    star_color = (255, 255, 0, 200)  # أصفر شفاف
    for i in range(8):
        angle = (i * 45) * math.pi / 180
        star_x = center_x + math.cos(angle) * (heart_size + 30)
        star_y = center_y + math.sin(angle) * (heart_size + 30)
        
        # رسم نجمة صغيرة
        star_size = 8
        star_points = []
        for j in range(10):
            star_angle = (j * 36) * math.pi / 180
            if j % 2 == 0:
                radius = star_size
            else:
                radius = star_size // 2
            
            px = star_x + math.cos(star_angle) * radius
            py = star_y + math.sin(star_angle) * radius
            star_points.append((px, py))
        
        draw.polygon(star_points, fill=star_color)
    
    # حفظ الصورة
    img.save('assets/mona_heart_icon.png', 'PNG')
    print("✅ تم إنشاء أيقونة Mona القلب الجميلة!")
    
    # إنشاء نسخة أصغر للمعاينة
    small_img = img.resize((512, 512), Image.Resampling.LANCZOS)
    small_img.save('assets/mona_icon_preview.png', 'PNG')
    print("✅ تم إنشاء معاينة الأيقونة!")

if __name__ == "__main__":
    create_mona_heart_icon()
