import 'package:flutter/foundation.dart';

class ChatMessage {
  final DateTime timestamp;
  String sender; // جعل sender قابل للتعديل لميزة تغيير الأسماء
  final String type; // 'text','image','video','audio','document','link','call','unknown'
  final String content; // نص أو اسم ملف أو رابط
  final String? filePath; // مسار الملف المحلي للوسائط

  ChatMessage({
    required this.timestamp,
    required this.sender,
    required this.type,
    required this.content,
    this.filePath,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.millisecondsSinceEpoch,
      'sender': sender,
      'type': type,
      'content': content,
      'filePath': filePath,
    };
  }

  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      sender: map['sender'],
      type: map['type'],
      content: map['content'],
      filePath: map['filePath'],
    );
  }

  static String determineMessageType(String content) {
    // تشخيص المحتوى
    debugPrint('🔍 تحليل نوع الرسالة: "${content.length > 100 ? content.substring(0, 100) + '...' : content}"');

    if (content.toLowerCase() == 'null' || content.isEmpty || content.trim().isEmpty) {
      debugPrint('❌ رسالة فارغة - مكالمة غير معروفة');
      return 'unknown_call'; // رسالة فارغة - غالباً مكالمة غير معروفة النوع
    }

    // تنظيف المحتوى من النصوص الإضافية والرموز غير المرئية
    String cleanContent = content.trim();

    // إزالة الرموز غير المرئية (RTL marks, etc.)
    cleanContent = cleanContent.replaceAll(RegExp(r'[\u200E\u200F\u202A\u202B\u202C\u202D\u202E]'), '');

    // إزالة النصوص الإضافية الشائعة
    cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(الملف مرفق\)'), '');
    cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(file attached\)'), '');
    cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(attached\)'), '');
    cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(مرفق\)'), '');

    final lowerContent = cleanContent.toLowerCase().trim();

    // التحقق من المكالمات
    if (lowerContent.contains('missed call') ||
        lowerContent.contains('مكالمة فائتة') ||
        lowerContent.contains('مكالمة') ||
        lowerContent.contains('call') ||
        lowerContent.contains('calling') ||
        lowerContent.contains('اتصال')) {
      return 'call';
    }

    // التحقق من الروابط
    if (content.startsWith('http://') || content.startsWith('https://') ||
        lowerContent.contains('www.') || lowerContent.contains('.com') ||
        lowerContent.contains('.org') || lowerContent.contains('.net')) {
      return 'link';
    }

    // التحقق من رسائل واتساب الخاصة
    if (lowerContent.contains('<media omitted>') ||
        lowerContent.contains('تم حذف هذه الرسالة') ||
        lowerContent.contains('this message was deleted') ||
        lowerContent.contains('الوسائط محذوفة')) {
      return 'deleted';
    }

    // التحقق من أسماء الملفات - بحث محسن للغاية
    if (content.contains('.')) {
      // أولاً: البحث عن امتدادات شائعة في واتساب
      final commonExtensions = ['pdf', 'doc', 'docx', 'apk', 'txt', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'mp4', '3gp', 'opus', 'mp3'];

      for (final ext in commonExtensions) {
        if (content.toLowerCase().contains('.$ext')) {
          debugPrint('✅ وجد امتداد: .$ext');
          // مستندات
          if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'zip', 'rar', '7z', 'apk'].contains(ext)) {
            debugPrint('📄 تم تصنيف كمستند: $ext');
            return 'document';
          }
          // صور
          if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg', 'heic', 'heif'].contains(ext)) {
            return 'image';
          }
          // فيديوهات
          if (['mp4', 'mov', 'avi', 'mkv', '3gp', 'webm', 'flv', 'wmv', 'm4v', 'mpg', 'mpeg'].contains(ext)) {
            return 'video';
          }
          // صوتيات
          if (['opus', 'mp3', 'm4a', 'wav', 'aac', 'ogg', 'wma', 'flac', 'amr'].contains(ext)) {
            return 'audio';
          }
        }
      }

      // ثانياً: البحث بالنمط المحسن
      final extensionPattern = RegExp(r'\.([a-zA-Z0-9]{2,5})', caseSensitive: false);
      final matches = extensionPattern.allMatches(content);

      for (final match in matches) {
        final extension = match.group(1)?.toLowerCase();
        if (extension != null) {
          // مستندات
          if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'zip', 'rar', '7z', 'apk'].contains(extension)) {
            return 'document';
          }
          // صور
          if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg', 'heic', 'heif'].contains(extension)) {
            return 'image';
          }
          // فيديوهات
          if (['mp4', 'mov', 'avi', 'mkv', '3gp', 'webm', 'flv', 'wmv', 'm4v', 'mpg', 'mpeg'].contains(extension)) {
            return 'video';
          }
          // صوتيات
          if (['opus', 'mp3', 'm4a', 'wav', 'aac', 'ogg', 'wma', 'flac', 'amr'].contains(extension)) {
            return 'audio';
          }
        }
      }
    }

    // التحقق من أنماط أسماء الملفات الشائعة في واتساب
    if (lowerContent.contains('img-') && (lowerContent.contains('.jpg') || lowerContent.contains('.jpeg') || lowerContent.contains('.png') || lowerContent.contains('.webp'))) {
      return 'image';
    }

    if (lowerContent.contains('vid-') && (lowerContent.contains('.mp4') || lowerContent.contains('.3gp') || lowerContent.contains('.mov'))) {
      return 'video';
    }

    if (lowerContent.contains('aud-') && (lowerContent.contains('.opus') || lowerContent.contains('.m4a') || lowerContent.contains('.aac'))) {
      return 'audio';
    }

    // رسائل صوتية (PTT - Push To Talk)
    if (lowerContent.contains('ptt-') && (lowerContent.contains('.opus') || lowerContent.contains('.m4a') || lowerContent.contains('.aac'))) {
      return 'audio';
    }

    if (lowerContent.contains('doc-') && lowerContent.contains('.')) {
      return 'document';
    }

    // أنماط إضافية لواتساب
    if (lowerContent.contains('whatsapp') && lowerContent.contains('image') && lowerContent.contains('.')) {
      return 'image';
    }

    if (lowerContent.contains('whatsapp') && lowerContent.contains('video') && lowerContent.contains('.')) {
      return 'video';
    }

    if (lowerContent.contains('whatsapp') && lowerContent.contains('audio') && lowerContent.contains('.')) {
      return 'audio';
    }

    // التحقق من الرموز التعبيرية فقط
    if (content.trim().length <= 10 && _containsOnlyEmojis(content)) {
      debugPrint('😀 تم تصنيف كرموز تعبيرية');
      return 'emoji';
    }

    debugPrint('📝 تم تصنيف كنص عادي');
    return 'text';
  }

  /// التحقق من أن النص يحتوي على رموز تعبيرية فقط
  static bool _containsOnlyEmojis(String text) {
    final cleanText = text.replaceAll(RegExp(r'\s'), ''); // إزالة المسافات
    if (cleanText.isEmpty) return false;

    // نمط بسيط للتحقق من الرموز التعبيرية
    final emojiPattern = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
    final matches = emojiPattern.allMatches(cleanText);

    return matches.isNotEmpty && matches.map((m) => m.group(0)!).join() == cleanText;
  }
}
