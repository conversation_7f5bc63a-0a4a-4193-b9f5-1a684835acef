<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة Mona</title>
</head>
<body>
    <canvas id="iconCanvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        // رسم أيقونة Mona الجميلة
        function drawMonaIcon() {
            // خلفية متدرجة
            const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
            gradient.addColorStop(0, '#FF1493'); // Deep Pink
            gradient.addColorStop(1, '#FF69B4'); // Hot Pink
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 512, 512);
            
            // رسم قلب
            ctx.fillStyle = '#FFB6C1'; // Light Pink
            ctx.beginPath();
            
            // رسم قلب بسيط
            const centerX = 256;
            const centerY = 200;
            const size = 80;
            
            // الجزء العلوي من القلب (دائرتان)
            ctx.arc(centerX - size/2, centerY - size/3, size/2, 0, Math.PI * 2);
            ctx.arc(centerX + size/2, centerY - size/3, size/2, 0, Math.PI * 2);
            ctx.fill();
            
            // الجزء السفلي من القلب (مثلث)
            ctx.beginPath();
            ctx.moveTo(centerX - size, centerY);
            ctx.lineTo(centerX + size, centerY);
            ctx.lineTo(centerX, centerY + size);
            ctx.closePath();
            ctx.fill();
            
            // نص "Mona"
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Mona', 256, 350);
            
            // إضافة ظل للنص
            ctx.fillStyle = '#000000';
            ctx.globalAlpha = 0.3;
            ctx.fillText('Mona', 258, 352);
            ctx.globalAlpha = 1.0;
            
            // نجوم صغيرة
            ctx.fillStyle = '#FFFF00';
            for (let i = 0; i < 8; i++) {
                const angle = (i * 45) * Math.PI / 180;
                const starX = centerX + Math.cos(angle) * 120;
                const starY = centerY + Math.sin(angle) * 120;
                
                ctx.beginPath();
                ctx.arc(starX, starY, 5, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'mona_heart_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // رسم الأيقونة عند تحميل الصفحة
        drawMonaIcon();
    </script>
</body>
</html>
