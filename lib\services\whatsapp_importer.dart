import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/chat.dart';
import '../models/chat_message.dart';

class WhatsAppImporter {
  // أنماط مختلفة للتعرف على الرسائل في ملف واتساب
  static final List<RegExp> _messagePatterns = [
    // النمط الأساسي: 12/03/2024, 11:14 - أحمد: مرحبا
    RegExp(r'(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}),\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط مع AM/PM: 12/03/2024, 11:14 PM - أحمد: مرحبا
    RegExp(r'(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}),\s*(\d{1,2}:\d{2})\s*(AM|PM|ص|م)\s*-\s*([^:]+):\s*(.*)'),

    // النمط مع فراغات إضافية
    RegExp(r'(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}),\s*(\d{1,2}:\d{2})\s*([^\-]*)\s*-\s*([^:]+):\s*(.*)'),

    // النمط البديل: [12/03/2024, 11:14:32] أحمد: مرحبا
    RegExp(r'\[(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}),\s*(\d{1,2}:\d{2}:\d{2})\]\s*([^:]+):\s*(.*)'),

    // النمط مع الثواني: 12/03/2024, 11:14:32 - أحمد: مرحبا
    RegExp(r'(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}),\s*(\d{1,2}:\d{2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط العربي: ١٢/٠٣/٢٠٢٤، ١١:١٤ - أحمد: مرحبا
    RegExp(r'([٠-٩]{1,2}[/\-][٠-٩]{1,2}[/\-][٠-٩]{2,4})،\s*([٠-٩]{1,2}:[٠-٩]{2})\s*-\s*([^:]+):\s*(.*)'),

    // نمط بدون فاصلة: 12/03/2024 11:14 - أحمد: مرحبا
    RegExp(r'(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4})\s+(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط العربي الجديد: 28‏/5‏/2025، 20:44 - Ali: مرحبا
    RegExp(r'(\d{1,2}‏[/\-]‏\d{1,2}‏[/\-]‏\d{2,4})،\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*‏?(.*)'),

    // النمط العربي مع AM/PM: 28‏/5‏/2025، 8:44 م - Ali: مرحبا
    RegExp(r'(\d{1,2}‏[/\-]‏\d{1,2}‏[/\-]‏\d{2,4})،\s*(\d{1,2}:\d{2})\s*(ص|م)\s*-\s*([^:]+):\s*‏?(.*)'),

    // النمط العربي مع الثواني: 28‏/5‏/2025، 20:44:15 - Ali: مرحبا
    RegExp(r'(\d{1,2}‏[/\-]‏\d{1,2}‏[/\-]‏\d{2,4})،\s*(\d{1,2}:\d{2}:\d{2})\s*-\s*([^:]+):\s*‏?(.*)'),

    // النمط العربي المحسن: 25‏/4‏/2025، 11:31 - Memo: صباحو
    RegExp(r'(\d{1,2}‏[/\-]\d{1,2}‏[/\-]\d{2,4})،\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط العربي مع فراغات: 25 ‏/4‏/ 2025، 11:31 - Memo: صباحو
    RegExp(r'(\d{1,2}\s*‏[/\-]\s*\d{1,2}\s*‏[/\-]\s*\d{2,4})،\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط الألماني/الأوروبي: 25.04.25, 13:06 - Firus: Ich wünsche dir dann
    RegExp(r'(\d{1,2}\.\d{1,2}\.\d{2,4}),\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط الأوروبي مع السنة الكاملة: 25.04.2025, 13:06 - Firus: Hallo
    RegExp(r'(\d{1,2}\.\d{1,2}\.\d{4}),\s*(\d{1,2}:\d{2})\s*-\s*([^:]+):\s*(.*)'),

    // النمط الأوروبي مع AM/PM: 25.04.25, 1:06 PM - Firus: Hallo
    RegExp(r'(\d{1,2}\.\d{1,2}\.\d{2,4}),\s*(\d{1,2}:\d{2})\s*(AM|PM|am|pm)\s*-\s*([^:]+):\s*(.*)'),
  ];

  /// استيراد ملف ZIP من واتساب
  static Future<Chat> importWhatsAppZip(String zipFilePath) async {
    if (kIsWeb) {
      return _importWhatsAppZipWeb(zipFilePath);
    } else {
      return _importWhatsAppZipMobile(zipFilePath);
    }
  }

  /// استيراد ZIP من البايتات مباشرة (للويب)
  static Future<Chat> importWhatsAppZipFromBytes(Uint8List bytes, String fileName) async {
    try {
      debugPrint('🌐 بدء استيراد ZIP من البايتات: $fileName');
      debugPrint('📦 حجم البايتات: ${bytes.length} بايت');

      final archive = ZipDecoder().decodeBytes(bytes);
      debugPrint('🗂️ عدد الملفات في ZIP: ${archive.length}');

      // البحث عن ملف النص
      String? txtContent;
      String? txtFileName;

      for (final file in archive) {
        if (file.isFile && file.name.endsWith('.txt')) {
          final data = file.content as List<int>;
          txtContent = utf8.decode(data);
          txtFileName = path.basename(file.name);
          debugPrint('📝 تم العثور على ملف نصي: $txtFileName');
          break;
        }
      }

      if (txtContent == null || txtFileName == null) {
        debugPrint('❌ لم يتم العثور على ملف .txt في ZIP');
        throw Exception('لم يتم العثور على ملف نصي في ZIP');
      }

      // تحليل النص مباشرة (بدون حفظ ملفات)
      final messages = await _parseWhatsAppTextWeb(txtContent);
      debugPrint('💬 تم تحليل ${messages.length} رسالة');

      if (messages.isEmpty) {
        throw Exception('لم يتم العثور على رسائل صالحة في الملف.');
      }

      // استخراج اسم المحادثة من اسم الملف
      final chatName = _extractChatName(txtFileName);
      debugPrint('👤 اسم المحادثة: $chatName');

      // إنشاء كائن المحادثة (بدون مجلد)
      final chat = Chat(
        id: 'whatsapp_web_${DateTime.now().millisecondsSinceEpoch}',
        name: '$chatName (ويب)',
        startDate: messages.first.timestamp,
        endDate: messages.last.timestamp,
        folderPath: '', // لا يوجد مجلد على الويب
        messages: messages,
      );

      debugPrint('✅ تم إنشاء كائن المحادثة بنجاح (ويب)');
      return chat;
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في استيراد ZIP من البايتات: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      throw Exception('خطأ في استيراد ملف ZIP: $e');
    }
  }

  /// استيراد ZIP للويب (محدود الوظائف)
  static Future<Chat> _importWhatsAppZipWeb(String zipFilePath) async {
    try {
      debugPrint('🌐 بدء استيراد ZIP على الويب: $zipFilePath');

      // قراءة ملف ZIP (على الويب، zipFilePath هو مسار مؤقت)
      final zipFile = File(zipFilePath);
      final bytes = await zipFile.readAsBytes();
      debugPrint('📦 تم قراءة ${bytes.length} بايت من ZIP');

      final archive = ZipDecoder().decodeBytes(bytes);
      debugPrint('🗂️ عدد الملفات في ZIP: ${archive.length}');

      // البحث عن ملف النص
      String? txtContent;
      String? txtFileName;

      for (final file in archive) {
        if (file.isFile && file.name.endsWith('.txt')) {
          final data = file.content as List<int>;
          txtContent = utf8.decode(data);
          txtFileName = path.basename(file.name);
          debugPrint('📝 تم العثور على ملف نصي: $txtFileName');
          break;
        }
      }

      if (txtContent == null || txtFileName == null) {
        debugPrint('❌ لم يتم العثور على ملف .txt في ZIP');
        throw Exception('لم يتم العثور على ملف نصي في ZIP');
      }

      // تحليل النص مباشرة (بدون حفظ ملفات)
      final messages = await _parseWhatsAppTextWeb(txtContent);
      debugPrint('💬 تم تحليل ${messages.length} رسالة');

      if (messages.isEmpty) {
        throw Exception('لم يتم العثور على رسائل صالحة في الملف.');
      }

      // استخراج اسم المحادثة من اسم الملف
      final chatName = _extractChatName(txtFileName);
      debugPrint('👤 اسم المحادثة: $chatName');

      // إنشاء كائن المحادثة (بدون مجلد)
      final chat = Chat(
        id: 'whatsapp_web_${DateTime.now().millisecondsSinceEpoch}',
        name: '$chatName (ويب)',
        startDate: messages.first.timestamp,
        endDate: messages.last.timestamp,
        folderPath: '', // لا يوجد مجلد على الويب
        messages: messages,
      );

      debugPrint('✅ تم إنشاء كائن المحادثة بنجاح (ويب)');
      return chat;
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في استيراد ملف ZIP على الويب: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      throw Exception('خطأ في استيراد ملف ZIP: $e');
    }
  }

  /// استيراد ZIP للهاتف (كامل الوظائف)
  static Future<Chat> _importWhatsAppZipMobile(String zipFilePath) async {
    try {
      debugPrint('📱 بدء استيراد ZIP على الهاتف: $zipFilePath');

      // التحقق من وجود الملف
      final zipFile = File(zipFilePath);
      if (!await zipFile.exists()) {
        throw Exception('ملف ZIP غير موجود: $zipFilePath');
      }

      debugPrint('📁 حجم الملف: ${await zipFile.length()} بايت');

      // قراءة ملف ZIP
      final bytes = await zipFile.readAsBytes();
      debugPrint('📦 تم قراءة ${bytes.length} بايت من ZIP');

      final archive = ZipDecoder().decodeBytes(bytes);
      debugPrint('🗂️ عدد الملفات في ZIP: ${archive.length}');

      // إنشاء مجلد للاستخراج
      final appDir = await getApplicationDocumentsDirectory();
      final zipFileName = path.basenameWithoutExtension(zipFilePath);
      final extractDir = Directory(path.join(appDir.path, 'chats', zipFileName));

      debugPrint('📂 مجلد الاستخراج: ${extractDir.path}');

      if (await extractDir.exists()) {
        await extractDir.delete(recursive: true);
        debugPrint('🗑️ تم حذف المجلد القديم');
      }
      await extractDir.create(recursive: true);
      debugPrint('📁 تم إنشاء مجلد جديد');

      // استخراج جميع الملفات
      String? txtFileName;
      int extractedCount = 0;
      final extractedFiles = <String>[];

      for (final file in archive) {
        if (file.isFile) {
          final filename = file.name;
          final data = file.content as List<int>;

          // تنظيف اسم الملف من المسارات الغريبة
          final cleanFilename = path.basename(filename);
          final extractedFile = File(path.join(extractDir.path, cleanFilename));

          try {
            // إنشاء المجلدات إذا لزم الأمر
            await extractedFile.parent.create(recursive: true);
            await extractedFile.writeAsBytes(data);
            extractedCount++;
            extractedFiles.add(extractedFile.path);
            debugPrint('📄 تم استخراج: $cleanFilename إلى ${extractedFile.path} (${data.length} بايت)');

            // البحث عن ملف النص
            if (cleanFilename.endsWith('.txt')) {
              txtFileName = cleanFilename;
              debugPrint('📝 تم العثور على ملف نصي: $cleanFilename');
            }
          } catch (e) {
            debugPrint('❌ خطأ في استخراج $cleanFilename: $e');
          }
        }
      }

      debugPrint('📁 الملفات المستخرجة:');
      for (final filePath in extractedFiles) {
        debugPrint('   - $filePath (موجود: ${File(filePath).existsSync()})');
      }

      debugPrint('✅ تم استخراج $extractedCount ملف');

      // إخفاء الوسائط من الاستوديو
      await _hideFromGallery(extractDir);

      if (txtFileName == null) {
        debugPrint('❌ لم يتم العثور على ملف .txt في ZIP');
        debugPrint('📋 الملفات الموجودة:');
        for (final file in archive) {
          debugPrint('   - ${file.name}');
        }
        throw Exception('لم يتم العثور على ملف نصي في ZIP');
      }

      // قراءة وتحليل ملف النص
      final txtFile = File(path.join(extractDir.path, txtFileName));
      debugPrint('📖 قراءة ملف النص: ${txtFile.path}');

      final messages = await _parseWhatsAppText(txtFile, extractDir.path);
      debugPrint('💬 تم تحليل ${messages.length} رسالة');

      if (messages.isEmpty) {
        debugPrint('❌ فشل الاستيراد: لم يتم العثور على رسائل صالحة');
        debugPrint('📄 اسم الملف: ${txtFile.path}');
        debugPrint('📊 حجم الملف: ${await txtFile.length()} بايت');

        // قراءة أول 1000 حرف للتشخيص
        final content = await txtFile.readAsString(encoding: utf8);
        final preview = content.length > 1000 ? content.substring(0, 1000) : content;
        debugPrint('📝 محتوى الملف (أول 1000 حرف):');
        debugPrint(preview);

        throw Exception('لم يتم العثور على رسائل صالحة في الملف.\n\n'
            'تفاصيل التشخيص:\n'
            '- اسم الملف: ${path.basename(txtFile.path)}\n'
            '- حجم الملف: ${await txtFile.length()} بايت\n'
            '- عدد الأسطر: ${content.split('\n').length}\n'
            '- أول 200 حرف: ${content.length > 200 ? content.substring(0, 200) : content}\n\n'
            'يرجى التحقق من تنسيق الملف أو إرسال هذه المعلومات للمطور.');
      }

      // استخراج اسم المحادثة من اسم الملف
      final chatName = _extractChatName(txtFileName);
      debugPrint('👤 اسم المحادثة: $chatName');

      // إنشاء كائن المحادثة
      final chat = Chat(
        id: 'whatsapp_${DateTime.now().millisecondsSinceEpoch}',
        name: chatName,
        startDate: messages.first.timestamp,
        endDate: messages.last.timestamp,
        folderPath: extractDir.path,
        messages: messages,
      );

      debugPrint('✅ تم إنشاء كائن المحادثة بنجاح');
      return chat;
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في استيراد ملف ZIP: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      throw Exception('خطأ في استيراد ملف ZIP: $e');
    }
  }

  /// تحليل النص من واتساب للويب (بدون ملفات وسائط)
  static Future<List<ChatMessage>> _parseWhatsAppTextWeb(String content) async {
    final lines = content.split('\n');
    final messages = <ChatMessage>[];

    debugPrint('🌐 تحليل النص للويب');
    debugPrint('📊 عدد الأسطر: ${lines.length}');

    int lineNumber = 0;
    for (final line in lines) {
      lineNumber++;
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      // جرب جميع الأنماط
      RegExpMatch? match;
      int patternIndex = -1;

      for (int i = 0; i < _messagePatterns.length; i++) {
        match = _messagePatterns[i].firstMatch(trimmedLine);
        if (match != null) {
          patternIndex = i;
          break;
        }
      }

      if (match != null) {
        try {
          String dateStr, timeStr, amPm = '', sender, messageContent;

          // تحليل المجموعات حسب النمط (نفس المنطق)
          switch (patternIndex) {
            case 0:
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              messageContent = match.group(4)!.trim();
              break;
            case 1:
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              amPm = match.group(3)?.trim() ?? '';
              sender = match.group(4)!.trim();
              messageContent = match.group(5)!.trim();
              break;
            // ... باقي الحالات مثل الدالة الأصلية
            default:
              continue;
          }

          final timestamp = _parseDateTime(dateStr, timeStr, amPm);
          final messageType = ChatMessage.determineMessageType(messageContent);

          final message = ChatMessage(
            timestamp: timestamp,
            sender: sender,
            type: messageType,
            content: messageContent,
            filePath: null, // لا توجد ملفات على الويب
          );

          messages.add(message);
        } catch (e) {
          debugPrint('❌ خطأ في تحليل السطر $lineNumber: $e');
          continue;
        }
      }
    }

    debugPrint('📊 إجمالي الرسائل المحللة (ويب): ${messages.length}');
    return messages;
  }

  /// تحليل ملف النص من واتساب
  static Future<List<ChatMessage>> _parseWhatsAppText(File txtFile, String mediaFolderPath) async {
    final content = await txtFile.readAsString(encoding: utf8);
    final lines = content.split('\n');
    final messages = <ChatMessage>[];

    debugPrint('📄 محتوى الملف (أول 500 حرف):');
    debugPrint(content.length > 500 ? '${content.substring(0, 500)}...' : content);
    debugPrint('📊 عدد الأسطر: ${lines.length}');

    // طباعة أول 5 أسطر للتشخيص
    debugPrint('📝 أول 5 أسطر:');
    for (int i = 0; i < lines.length && i < 5; i++) {
      debugPrint('السطر ${i + 1}: ${lines[i]}');
    }

    int lineNumber = 0;
    for (final line in lines) {
      lineNumber++;
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      // جرب جميع الأنماط
      RegExpMatch? match;
      int patternIndex = -1;

      // طباعة السطر الحالي للتشخيص (أول 10 أسطر فقط)
      if (lineNumber <= 10) {
        debugPrint('🔍 تحليل السطر $lineNumber: "$trimmedLine"');
      }

      for (int i = 0; i < _messagePatterns.length; i++) {
        match = _messagePatterns[i].firstMatch(trimmedLine);
        if (match != null) {
          patternIndex = i;
          if (lineNumber <= 10) {
            debugPrint('✅ تم العثور على تطابق مع النمط $i');
            debugPrint('📊 المجموعات: ${match.groupCount}');
            for (int j = 1; j <= match.groupCount; j++) {
              debugPrint('   المجموعة $j: "${match.group(j)}"');
            }
          }
          break;
        }
      }

      // إذا لم يتم العثور على تطابق، اطبع تفاصيل أكثر
      if (match == null && lineNumber <= 10) {
        debugPrint('❌ لم يتم العثور على تطابق للسطر $lineNumber');
        debugPrint('📝 السطر: "$trimmedLine"');
        debugPrint('📏 طول السطر: ${trimmedLine.length}');
        debugPrint('🔤 أول 20 حرف: "${trimmedLine.length > 20 ? trimmedLine.substring(0, 20) : trimmedLine}"');

        // اطبع الأحرف بالكود Unicode
        if (trimmedLine.isNotEmpty) {
          final firstChars = trimmedLine.length > 10 ? trimmedLine.substring(0, 10) : trimmedLine;
          final unicodeCodes = firstChars.runes.map((r) => 'U+${r.toRadixString(16).toUpperCase().padLeft(4, '0')}').join(' ');
          debugPrint('🔢 أكواد Unicode للأحرف الأولى: $unicodeCodes');
        }
      }

      if (match != null) {
        try {
          String dateStr, timeStr, amPm = '', sender, content;

          // تحليل المجموعات حسب النمط
          switch (patternIndex) {
            case 0: // النمط الأساسي
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 1: // النمط مع AM/PM
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              amPm = match.group(3)?.trim() ?? '';
              sender = match.group(4)!.trim();
              content = match.group(5)!.trim();
              break;
            case 2: // النمط مع فراغات إضافية
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              amPm = match.group(3)?.trim() ?? '';
              sender = match.group(4)!.trim();
              content = match.group(5)!.trim();
              break;
            case 3: // النمط البديل مع أقواس
              dateStr = match.group(1)!;
              timeStr = match.group(2)!.substring(0, 5); // إزالة الثواني
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 4: // النمط مع الثواني
              dateStr = match.group(1)!;
              timeStr = match.group(2)!.substring(0, 5); // إزالة الثواني
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 5: // النمط العربي
              dateStr = _convertArabicNumbers(match.group(1)!);
              timeStr = _convertArabicNumbers(match.group(2)!);
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 6: // نمط بدون فاصلة
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 7: // النمط العربي الجديد: 28‏/5‏/2025، 20:44 - Ali: مرحبا
              dateStr = match.group(1)!.replaceAll('‏', ''); // إزالة علامات الاتجاه
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim().replaceAll('‏', ''); // إزالة علامات الاتجاه
              break;
            case 8: // النمط العربي مع AM/PM: 28‏/5‏/2025، 8:44 م - Ali: مرحبا
              dateStr = match.group(1)!.replaceAll('‏', ''); // إزالة علامات الاتجاه
              timeStr = match.group(2)!;
              amPm = match.group(3)?.trim() ?? '';
              sender = match.group(4)!.trim();
              content = match.group(5)!.trim().replaceAll('‏', ''); // إزالة علامات الاتجاه
              break;
            case 9: // النمط العربي مع الثواني: 28‏/5‏/2025، 20:44:15 - Ali: مرحبا
              dateStr = match.group(1)!.replaceAll('‏', ''); // إزالة علامات الاتجاه
              timeStr = match.group(2)!.substring(0, 5); // إزالة الثواني
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim().replaceAll('‏', ''); // إزالة علامات الاتجاه
              break;
            case 10: // النمط العربي المحسن: 25‏/4‏/2025، 11:31 - Memo: صباحو
              dateStr = match.group(1)!.replaceAll('‏', '').replaceAll(' ', ''); // إزالة علامات الاتجاه والفراغات
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 11: // النمط العربي مع فراغات: 25 ‏/4‏/ 2025، 11:31 - Memo: صباحو
              dateStr = match.group(1)!.replaceAll('‏', '').replaceAll(' ', ''); // إزالة علامات الاتجاه والفراغات
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 12: // النمط الألماني/الأوروبي: 25.04.25, 13:06 - Firus: Ich wünsche dir dann
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 13: // النمط الأوروبي مع السنة الكاملة: 25.04.2025, 13:06 - Firus: Hallo
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              sender = match.group(3)!.trim();
              content = match.group(4)!.trim();
              break;
            case 14: // النمط الأوروبي مع AM/PM: 25.04.25, 1:06 PM - Firus: Hallo
              dateStr = match.group(1)!;
              timeStr = match.group(2)!;
              amPm = match.group(3)?.trim() ?? '';
              sender = match.group(4)!.trim();
              content = match.group(5)!.trim();
              break;
            default:
              continue;
          }

          final timestamp = _parseDateTime(dateStr, timeStr, amPm);
          final messageType = ChatMessage.determineMessageType(content);

          String? filePath;
          if (['image', 'video', 'audio', 'document'].contains(messageType)) {
            // تنظيف اسم الملف من النصوص الإضافية
            String cleanContent = content.trim();

            // إزالة الرموز غير المرئية (RTL marks, etc.)
            cleanContent = cleanContent.replaceAll(RegExp(r'[\u200E\u200F\u202A\u202B\u202C\u202D\u202E]'), '');

            // إزالة النصوص الإضافية الشائعة
            cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(الملف مرفق\)'), '');
            cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(file attached\)'), '');
            cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(attached\)'), '');
            cleanContent = cleanContent.replaceAll(RegExp(r'\s*\(مرفق\)'), '');
            cleanContent = cleanContent.trim();

            debugPrint('🧹 اسم الملف الأصلي: "$content"');
            debugPrint('🧹 اسم الملف المنظف: "$cleanContent"');

            // البحث عن الملف في مجلد الوسائط
            debugPrint('🔍 البحث عن ملف الوسائط: $cleanContent في $mediaFolderPath');
            filePath = await _findMediaFile(cleanContent, mediaFolderPath);
            if (filePath != null) {
              debugPrint('✅ تم العثور على ملف الوسائط: $filePath');
              // التحقق من وجود الملف فعلياً
              if (File(filePath).existsSync()) {
                debugPrint('✅ الملف موجود فعلياً: $filePath');
              } else {
                debugPrint('❌ الملف غير موجود رغم العثور على المسار: $filePath');
                filePath = null; // إلغاء المسار إذا كان الملف غير موجود
              }
            } else {
              debugPrint('❌ لم يتم العثور على ملف الوسائط: $cleanContent');

              // للفيديوهات والمستندات والملفات الأخرى، جرب البحث الشامل
              if (messageType == 'video') {
                debugPrint('🎥 البحث الشامل عن الفيديو...');
                filePath = await _findAnyFileByName(cleanContent, mediaFolderPath);
                if (filePath != null) {
                  debugPrint('✅ تم العثور على الفيديو بالبحث الشامل: $filePath');
                } else {
                  debugPrint('❌ لم يتم العثور على الفيديو حتى بالبحث الشامل');
                }
              } else if (messageType == 'image') {
                debugPrint('📷 البحث الشامل عن الصورة...');
                filePath = await _findAnyFileByName(cleanContent, mediaFolderPath);
                if (filePath != null) {
                  debugPrint('✅ تم العثور على الصورة بالبحث الشامل: $filePath');
                } else {
                  debugPrint('❌ لم يتم العثور على الصورة حتى بالبحث الشامل');
                }
              } else if (messageType == 'audio') {
                debugPrint('🎧 البحث الشامل عن الصوت...');
                filePath = await _findAnyFileByName(cleanContent, mediaFolderPath);
                if (filePath != null) {
                  debugPrint('✅ تم العثور على الصوت بالبحث الشامل: $filePath');
                } else {
                  debugPrint('❌ لم يتم العثور على الصوت حتى بالبحث الشامل');
                }
              } else if (messageType == 'document') {
                debugPrint('📄 البحث الشامل عن المستند...');
                filePath = await _findAnyFileByName(cleanContent, mediaFolderPath);
                if (filePath != null) {
                  debugPrint('✅ تم العثور على المستند بالبحث الشامل: $filePath');
                } else {
                  debugPrint('❌ لم يتم العثور على المستند حتى بالبحث الشامل');
                }
              }
            }
          }

          final message = ChatMessage(
            timestamp: timestamp,
            sender: sender,
            type: messageType,
            content: content,
            filePath: filePath,
          );

          messages.add(message);
          debugPrint('✅ رسالة ${messages.length}: $sender - ${content.substring(0, content.length > 50 ? 50 : content.length)}...');
        } catch (e) {
          debugPrint('❌ خطأ في تحليل السطر $lineNumber: $e');
          debugPrint('📝 السطر: $trimmedLine');
          continue;
        }
      } else {
        // طباعة الأسطر التي لم يتم تحليلها (أول 10 فقط)
        if (lineNumber <= 10) {
          debugPrint('⚠️ لم يتم تحليل السطر $lineNumber: $trimmedLine');
        }
      }
    }

    debugPrint('📊 إجمالي الرسائل المحللة: ${messages.length}');
    debugPrint('📊 إجمالي الأسطر المعالجة: $lineNumber');
    debugPrint('📊 نسبة النجاح: ${messages.length}/$lineNumber = ${messages.isNotEmpty ? (messages.length / lineNumber * 100).toStringAsFixed(1) : 0}%');

    if (messages.isEmpty) {
      debugPrint('❌ لم يتم العثور على أي رسائل صالحة!');
      debugPrint('🔍 تحقق من:');
      debugPrint('   1. تنسيق التاريخ والوقت');
      debugPrint('   2. وجود علامة "-" بين الوقت والاسم');
      debugPrint('   3. وجود ":" بين الاسم والمحتوى');
      debugPrint('   4. أكواد Unicode للأحرف الخاصة');
    }

    return messages;
  }

  /// تحليل التاريخ والوقت من نص واتساب
  static DateTime _parseDateTime(String dateStr, String timeStr, String amPm) {
    try {
      debugPrint('🕐 تحليل التاريخ: $dateStr $timeStr $amPm');

      // تنظيف التاريخ من علامات الاتجاه العربية
      final cleanDateStr = dateStr.replaceAll('‏', '');

      // تحليل التاريخ
      final dateParts = cleanDateStr.split(RegExp(r'[/\-\.]')); // إضافة النقطة للنمط الأوروبي
      int day, month, year;

      if (dateParts.length == 3) {
        // جرب ترتيبات مختلفة للتاريخ
        final part1 = int.parse(dateParts[0]);
        final part2 = int.parse(dateParts[1]);
        final part3 = int.parse(dateParts[2]);

        debugPrint('🔍 أجزاء التاريخ: $part1/$part2/$part3');

        if (part3 > 31) {
          // التنسيق: DD/MM/YYYY أو MM/DD/YYYY أو DD.MM.YYYY (أوروبي)
          if (cleanDateStr.contains('.')) {
            // النمط الأوروبي: DD.MM.YYYY
            day = part1;
            month = part2;
            debugPrint('📅 تنسيق أوروبي DD.MM.YYYY');
          } else if (part1 > 12) {
            // DD/MM/YYYY
            day = part1;
            month = part2;
            debugPrint('📅 تنسيق DD/MM/YYYY');
          } else if (part2 > 12) {
            // MM/DD/YYYY
            day = part2;
            month = part1;
            debugPrint('📅 تنسيق MM/DD/YYYY');
          } else {
            // افتراض DD/MM/YYYY (التنسيق العربي الشائع)
            day = part1;
            month = part2;
            debugPrint('📅 افتراض DD/MM/YYYY');
          }
          year = part3;
        } else {
          // التنسيق: DD/MM/YY أو MM/DD/YY أو DD.MM.YY (أوروبي)
          if (cleanDateStr.contains('.')) {
            // النمط الأوروبي: DD.MM.YY
            day = part1;
            month = part2;
            debugPrint('📅 تنسيق أوروبي DD.MM.YY');
          } else if (part1 > 12) {
            // DD/MM/YY
            day = part1;
            month = part2;
            debugPrint('📅 تنسيق DD/MM/YY');
          } else if (part2 > 12) {
            // MM/DD/YY
            day = part2;
            month = part1;
            debugPrint('📅 تنسيق MM/DD/YY');
          } else {
            // افتراض DD/MM/YY (التنسيق العربي الشائع)
            day = part1;
            month = part2;
            debugPrint('📅 افتراض DD/MM/YY');
          }
          year = part3;

          // إذا كانت السنة بصيغة مختصرة
          if (year < 100) {
            year += (year < 50) ? 2000 : 1900;
          }
        }

        debugPrint('📅 النتيجة: يوم=$day، شهر=$month، سنة=$year');
      } else {
        // قيم افتراضية في حالة فشل التحليل
        day = 1;
        month = 1;
        year = 2024;
        debugPrint('❌ فشل تحليل التاريخ، استخدام قيم افتراضية');
      }

      // تحليل الوقت
      final timeParts = timeStr.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      // تحويل من 12 ساعة إلى 24 ساعة إذا لزم الأمر
      if (amPm.contains('م') || amPm.toLowerCase().contains('pm')) {
        if (hour != 12) hour += 12;
      } else if (amPm.contains('ص') || amPm.toLowerCase().contains('am')) {
        if (hour == 12) hour = 0;
      }

      final result = DateTime(year, month, day, hour, minute);
      debugPrint('✅ تم تحليل التاريخ: $result');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في تحليل التاريخ: $dateStr $timeStr $amPm - $e');
      return DateTime.now();
    }
  }

  /// تحويل الأرقام العربية إلى إنجليزية
  static String _convertArabicNumbers(String text) {
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    const englishNumbers = '0123456789';

    String result = text;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  /// استخراج اسم المحادثة من اسم الملف
  static String _extractChatName(String fileName) {
    String name = path.basenameWithoutExtension(fileName);

    // إزالة البادئات الشائعة
    if (name.startsWith('WhatsApp Chat with ')) {
      name = name.substring('WhatsApp Chat with '.length);
    } else if (name.startsWith('WhatsApp Chat - ')) {
      name = name.substring('WhatsApp Chat - '.length);
    } else if (name.startsWith('محادثة واتساب مع ')) {
      name = name.substring('محادثة واتساب مع '.length);
    }

    return name.trim();
  }

  /// البحث عن ملف الوسائط في مجلد الاستخراج
  static Future<String?> _findMediaFile(String fileName, String mediaFolderPath) async {
    try {
      debugPrint('🔍 البحث عن ملف الوسائط: $fileName في $mediaFolderPath');

      // تنظيف اسم الملف
      final cleanFileName = path.basename(fileName);
      debugPrint('🔍 اسم الملف المنظف: $cleanFileName');

      // البحث المباشر
      final directFile = File(path.join(mediaFolderPath, cleanFileName));
      if (await directFile.exists()) {
        debugPrint('✅ تم العثور على الملف مباشرة: ${directFile.path}');
        return directFile.path;
      }

      // البحث في المجلدات الفرعية
      final mediaDir = Directory(mediaFolderPath);
      if (!await mediaDir.exists()) {
        debugPrint('❌ مجلد الوسائط غير موجود: $mediaFolderPath');
        return null;
      }

      // قائمة جميع الملفات الموجودة للمقارنة
      final allFiles = <String>[];
      final allFilePaths = <String>[];

      await for (final entity in mediaDir.list(recursive: false)) {
        if (entity is File) {
          final entityName = path.basename(entity.path);
          allFiles.add(entityName);
          allFilePaths.add(entity.path);

          debugPrint('📄 ملف موجود: $entityName في ${entity.path}');

          // مطابقة دقيقة 100% بالاسم المنظف
          if (entityName == cleanFileName) {
            debugPrint('✅ مطابقة دقيقة 100%: $cleanFileName -> ${entity.path}');
            return entity.path;
          }

          // مطابقة دقيقة بالاسم الأصلي
          if (entityName == fileName) {
            debugPrint('✅ مطابقة دقيقة بالاسم الأصلي: $fileName -> ${entity.path}');
            return entity.path;
          }

          // مطابقة غير حساسة للحالة
          if (entityName.toLowerCase() == cleanFileName.toLowerCase()) {
            debugPrint('✅ مطابقة غير حساسة للحالة: $cleanFileName -> ${entity.path}');
            return entity.path;
          }

          // مطابقة بدون امتداد
          final fileNameWithoutExt = path.basenameWithoutExtension(cleanFileName).toLowerCase();
          final entityNameWithoutExt = path.basenameWithoutExtension(entityName).toLowerCase();
          if (fileNameWithoutExt == entityNameWithoutExt) {
            debugPrint('✅ مطابقة بدون امتداد: ${entity.path}');
            return entity.path;
          }

          // مطابقة أنماط واتساب الشائعة
          final lowerFileName = cleanFileName.toLowerCase();
          final lowerEntityName = entityName.toLowerCase();

          // مطابقة بالتاريخ والنوع (IMG-YYYYMMDD-WA)
          final imgPattern = RegExp(r'img-(\d{8})-wa(\d+)');
          final vidPattern = RegExp(r'vid-(\d{8})-wa(\d+)');
          final audPattern = RegExp(r'(aud|ptt)-(\d{8})-wa(\d+)');

          final fileImgMatch = imgPattern.firstMatch(lowerFileName);
          final entityImgMatch = imgPattern.firstMatch(lowerEntityName);

          if (fileImgMatch != null && entityImgMatch != null) {
            final fileDate = fileImgMatch.group(1);
            final entityDate = entityImgMatch.group(1);
            if (fileDate == entityDate) {
              debugPrint('✅ مطابقة صورة بالتاريخ: ${entity.path}');
              return entity.path;
            }
          }

          final fileVidMatch = vidPattern.firstMatch(lowerFileName);
          final entityVidMatch = vidPattern.firstMatch(lowerEntityName);

          if (fileVidMatch != null && entityVidMatch != null) {
            final fileDate = fileVidMatch.group(1);
            final entityDate = entityVidMatch.group(1);
            if (fileDate == entityDate) {
              debugPrint('✅ مطابقة فيديو بالتاريخ: ${entity.path}');
              return entity.path;
            }
          }

          final fileAudMatch = audPattern.firstMatch(lowerFileName);
          final entityAudMatch = audPattern.firstMatch(lowerEntityName);

          if (fileAudMatch != null && entityAudMatch != null) {
            final fileDate = fileAudMatch.group(2);
            final entityDate = entityAudMatch.group(2);
            if (fileDate == entityDate) {
              debugPrint('✅ مطابقة صوت بالتاريخ: ${entity.path}');
              return entity.path;
            }
          }

          // مطابقة عامة بالنوع
          if (lowerFileName.contains('img-') && lowerEntityName.contains('img-')) {
            debugPrint('✅ مطابقة نمط صورة: ${entity.path}');
            return entity.path;
          }

          if (lowerFileName.contains('vid-') && lowerEntityName.contains('vid-')) {
            debugPrint('✅ مطابقة نمط فيديو: ${entity.path}');
            return entity.path;
          }

          if ((lowerFileName.contains('aud-') || lowerFileName.contains('ptt-')) &&
              (lowerEntityName.contains('aud-') || lowerEntityName.contains('ptt-'))) {
            debugPrint('✅ مطابقة نمط صوت: ${entity.path}');
            return entity.path;
          }
        }
      }

      debugPrint('❌ لم يتم العثور على ملف الوسائط: $cleanFileName');
      debugPrint('📋 الملفات المتاحة: ${allFiles.join(', ')}');
      debugPrint('📋 المسارات الكاملة:');
      for (final filePath in allFilePaths) {
        debugPrint('   - $filePath');
      }
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في البحث عن ملف الوسائط: $e');
      return null;
    }
  }

  /// البحث الشامل عن أي ملف بالاسم (للمستندات والملفات الأخرى)
  static Future<String?> _findAnyFileByName(String fileName, String mediaFolderPath) async {
    try {
      debugPrint('🔍 البحث الشامل عن ملف: $fileName في $mediaFolderPath');

      final mediaDir = Directory(mediaFolderPath);
      if (!await mediaDir.exists()) {
        debugPrint('❌ مجلد الوسائط غير موجود: $mediaFolderPath');
        return null;
      }

      // تنظيف اسم الملف
      final cleanFileName = path.basename(fileName).toLowerCase();
      final fileNameWithoutExt = path.basenameWithoutExtension(cleanFileName);

      debugPrint('🔍 البحث عن: $cleanFileName (بدون امتداد: $fileNameWithoutExt)');

      // البحث في جميع الملفات
      await for (final entity in mediaDir.list(recursive: false)) {
        if (entity is File) {
          final entityName = path.basename(entity.path).toLowerCase();
          final entityNameWithoutExt = path.basenameWithoutExtension(entityName);

          debugPrint('📄 فحص ملف: $entityName');

          // مطابقة دقيقة بالاسم الكامل
          if (entityName == cleanFileName) {
            debugPrint('✅ مطابقة دقيقة بالاسم الكامل: ${entity.path}');
            return entity.path;
          }

          // مطابقة بدون امتداد
          if (entityNameWithoutExt == fileNameWithoutExt) {
            debugPrint('✅ مطابقة بدون امتداد: ${entity.path}');
            return entity.path;
          }

          // مطابقة جزئية للمستندات
          if (entityName.contains(fileNameWithoutExt) || fileNameWithoutExt.contains(entityNameWithoutExt)) {
            debugPrint('✅ مطابقة جزئية: ${entity.path}');
            return entity.path;
          }
        }
      }

      debugPrint('❌ لم يتم العثور على الملف بالبحث الشامل: $fileName');
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في البحث الشامل: $e');
      return null;
    }
  }

  /// تحديد نوع المرسل (أنا أم الطرف الآخر)
  static bool isMyMessage(String sender, List<String> allSenders) {
    // منطق بسيط: إذا كان المرسل هو الأكثر تكراراً، فهو المستخدم الحالي
    final senderCounts = <String, int>{};
    for (final s in allSenders) {
      senderCounts[s] = (senderCounts[s] ?? 0) + 1;
    }

    final mostFrequentSender = senderCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return sender == mostFrequentSender;
  }

  /// استيراد محادثة واتساب من مجلد مفكوك (أسرع من ZIP)
  static Future<Chat> importWhatsAppFolder(String folderPath) async {
    debugPrint('🚀 بدء استيراد المجلد: $folderPath');

    final folder = Directory(folderPath);
    if (!await folder.exists()) {
      throw Exception('المجلد غير موجود: $folderPath');
    }

    // البحث عن ملف النص (.txt) والمجلدات
    final files = await folder.list(recursive: true).toList();
    File? chatFile;
    Directory? mediaFolder;

    debugPrint('📂 محتويات المجلد:');
    for (final entity in files) {
      debugPrint('  - ${entity.path}');

      if (entity is File && entity.path.toLowerCase().endsWith('.txt')) {
        chatFile = entity;
        debugPrint('📄 تم العثور على ملف المحادثة: ${entity.path}');
      } else if (entity is Directory) {
        // البحث عن مجلد الوسائط بطرق مختلفة
        final dirName = path.basename(entity.path).toLowerCase();
        final fullPath = entity.path.toLowerCase();

        if (dirName.contains('whatsapp') ||
            dirName.contains('media') ||
            dirName.contains('وسائط') ||
            dirName.contains('ملفات') ||
            fullPath.contains('whatsapp') ||
            // أسماء مجلدات واتساب الشائعة
            dirName == 'whatsapp images' ||
            dirName == 'whatsapp video' ||
            dirName == 'whatsapp audio' ||
            dirName == 'whatsapp documents' ||
            dirName.startsWith('whatsapp')) {
          mediaFolder = entity;
          debugPrint('📁 تم العثور على مجلد الوسائط: ${entity.path}');
        }
      }
    }

    // إذا لم نجد ملف txt، ابحث في المجلدات الفرعية
    if (chatFile == null) {
      debugPrint('🔍 البحث في المجلدات الفرعية عن ملف .txt...');
      await for (final entity in folder.list(recursive: true)) {
        if (entity is File && entity.path.toLowerCase().endsWith('.txt')) {
          chatFile = entity;
          debugPrint('📄 تم العثور على ملف المحادثة في مجلد فرعي: ${entity.path}');
          break;
        }
      }
    }

    if (chatFile == null) {
      throw Exception('لم يتم العثور على ملف المحادثة (.txt) في المجلد');
    }

    // قراءة محتوى ملف المحادثة
    final chatContent = await chatFile.readAsString(encoding: utf8);
    debugPrint('📖 تم قراءة ملف المحادثة: ${chatContent.length} حرف');

    // تحليل المحادثة
    final messages = await _parseWhatsAppText(chatFile, mediaFolder?.path ?? '');
    debugPrint('💬 تم تحليل ${messages.length} رسالة');

    if (messages.isEmpty) {
      throw Exception('لم يتم العثور على رسائل صالحة في الملف');
    }

    // إنشاء مجلد التطبيق للمحادثة
    final appDir = await getApplicationDocumentsDirectory();
    final chatName = path.basenameWithoutExtension(chatFile.path);
    final chatId = DateTime.now().millisecondsSinceEpoch.toString();
    final targetFolder = Directory(path.join(appDir.path, 'chats', chatId));

    if (!await targetFolder.exists()) {
      await targetFolder.create(recursive: true);
    }

    // نسخ ملف المحادثة
    final targetChatFile = File(path.join(targetFolder.path, '${chatName}.txt'));
    await chatFile.copy(targetChatFile.path);

    // نسخ مجلد الوسائط إذا وُجد
    if (mediaFolder != null) {
      final targetMediaFolder = Directory(path.join(targetFolder.path, 'media'));
      await _copyDirectory(mediaFolder, targetMediaFolder);

      // إخفاء الوسائط من الاستوديو بإضافة ملف .nomedia
      await _hideFromGallery(targetMediaFolder);
      debugPrint('📁 تم نسخ مجلد الوسائط وإخفاؤه من الاستوديو');
    }

    // الرسائل جاهزة مع الروابط من التحليل الأصلي
    debugPrint('🔗 الرسائل جاهزة مع روابط الملفات');

    // إنشاء كائن المحادثة
    final chat = Chat(
      id: chatId,
      name: chatName,
      startDate: messages.first.timestamp,
      endDate: messages.last.timestamp,
      folderPath: targetFolder.path,
      messages: messages,
    );

    debugPrint('✅ تم استيراد المحادثة بنجاح: ${chat.name}');
    return chat;
  }

  /// نسخ مجلد بالكامل مع محتوياته
  static Future<void> _copyDirectory(Directory source, Directory destination) async {
    if (!await destination.exists()) {
      await destination.create(recursive: true);
    }

    await for (final entity in source.list(recursive: false)) {
      if (entity is File) {
        final targetFile = File(path.join(destination.path, path.basename(entity.path)));
        await entity.copy(targetFile.path);
      } else if (entity is Directory) {
        final targetDir = Directory(path.join(destination.path, path.basename(entity.path)));
        await _copyDirectory(entity, targetDir);
      }
    }
  }

  /// إخفاء الوسائط من تطبيق الاستوديو (Gallery)
  static Future<void> _hideFromGallery(Directory mediaFolder) async {
    try {
      // إنشاء ملف .nomedia في المجلد الرئيسي
      final nomediaFile = File(path.join(mediaFolder.path, '.nomedia'));
      if (!await nomediaFile.exists()) {
        await nomediaFile.create();
        debugPrint('🔒 تم إنشاء ملف .nomedia في: ${mediaFolder.path}');
      }

      // إنشاء ملف .nomedia في جميع المجلدات الفرعية
      await for (final entity in mediaFolder.list(recursive: true)) {
        if (entity is Directory) {
          final subNomediaFile = File(path.join(entity.path, '.nomedia'));
          if (!await subNomediaFile.exists()) {
            await subNomediaFile.create();
            debugPrint('🔒 تم إنشاء ملف .nomedia في: ${entity.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في إخفاء الوسائط من الاستوديو: $e');
    }
  }
}
