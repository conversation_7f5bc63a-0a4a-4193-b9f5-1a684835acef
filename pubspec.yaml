name: whatsapp_simple
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.0+22

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # File handling and storage
  file_picker: ^8.0.0+1
  archive: ^3.4.10
  path_provider: ^2.1.2
  path: ^1.9.0
  shared_preferences: ^2.2.2

  # Media handling
  photo_view: ^0.14.0
  video_player: ^2.8.2
  video_thumbnail: ^0.5.3
  just_audio: ^0.9.36
  url_launcher: ^6.2.4

  # UI and localization
  intl: ^0.20.2
  share_plus: ^11.0.0

  # حماية بالبصمة/Face ID
  local_auth: ^2.3.0

  # معرض صور متقدم (محلي فقط)
  # cached_network_image: ^3.4.1  # تم إزالته لأنه يحتاج الإنترنت

  # مشغل صوت محسن
  audioplayers: ^6.1.0

  # عرض المستندات
  open_file: ^3.5.7

  # إحصائيات وتحليل
  fl_chart: ^0.69.0

  # عارض PDF
  flutter_pdfview: ^1.3.2



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/memo_icon.jpeg  # أيقونة التطبيق الجديدة

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "memo_launcher_icon"
  ios: true
  image_path: "assets/memo_icon.jpeg"  # أيقونة التطبيق الجديدة
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/memo_icon.jpeg"
    background_color: "#FFFFFF"  # خلفية بيضاء
    theme_color: "#25D366"       # أخضر واتساب
  windows:
    generate: true
    image_path: "assets/memo_icon.jpeg"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/memo_icon.jpeg"

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
