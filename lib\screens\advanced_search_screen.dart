import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';
import '../models/chat.dart';
import '../widgets/message_bubble.dart';

class AdvancedSearchScreen extends StatefulWidget {
  final Chat chat;
  final List<ChatMessage> messages;

  const AdvancedSearchScreen({
    super.key,
    required this.chat,
    required this.messages,
  });

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _senderController = TextEditingController();
  
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedType = 'all';
  
  List<ChatMessage> _searchResults = [];
  bool _isSearching = false;

  final List<Map<String, String>> _messageTypes = [
    {'value': 'all', 'label': 'جميع الأنواع'},
    {'value': 'text', 'label': 'نصوص'},
    {'value': 'image', 'label': 'صور'},
    {'value': 'video', 'label': 'فيديوهات'},
    {'value': 'audio', 'label': 'صوتيات'},
    {'value': 'document', 'label': 'مستندات'},
    {'value': 'link', 'label': 'روابط'},
    {'value': 'call', 'label': 'مكالمات'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0B141B),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1F2C34),
        title: Text(
          'البحث المتقدم - ${widget.chat.name}',
          style: const TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // منطقة البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFF1F2C34),
            child: Column(
              children: [
                // حقل البحث النصي
                TextField(
                  controller: _searchController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'ابحث في المحتوى...',
                    hintStyle: TextStyle(color: Colors.white54),
                    prefixIcon: Icon(Icons.search, color: Colors.white54),
                    border: OutlineInputBorder(),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white54),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF25D366)),
                    ),
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // حقل البحث بالمرسل
                TextField(
                  controller: _senderController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'ابحث بالمرسل...',
                    hintStyle: TextStyle(color: Colors.white54),
                    prefixIcon: Icon(Icons.person, color: Colors.white54),
                    border: OutlineInputBorder(),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white54),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF25D366)),
                    ),
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // اختيار نوع الرسالة
                DropdownButtonFormField<String>(
                  value: _selectedType,
                  style: const TextStyle(color: Colors.white),
                  dropdownColor: const Color(0xFF1F2C34),
                  decoration: const InputDecoration(
                    labelText: 'نوع الرسالة',
                    labelStyle: TextStyle(color: Colors.white54),
                    border: OutlineInputBorder(),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white54),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF25D366)),
                    ),
                  ),
                  items: _messageTypes.map((type) => DropdownMenuItem(
                    value: type['value'],
                    child: Text(type['label']!),
                  )).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  },
                ),
                
                const SizedBox(height: 12),
                
                // اختيار التواريخ
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectStartDate(),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.white54),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, color: Colors.white54, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                _startDate != null 
                                    ? DateFormat('dd/MM/yyyy').format(_startDate!)
                                    : 'من تاريخ',
                                style: TextStyle(
                                  color: _startDate != null ? Colors.white : Colors.white54,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEndDate(),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.white54),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, color: Colors.white54, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                _endDate != null 
                                    ? DateFormat('dd/MM/yyyy').format(_endDate!)
                                    : 'إلى تاريخ',
                                style: TextStyle(
                                  color: _endDate != null ? Colors.white : Colors.white54,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // أزرار البحث والمسح
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _performSearch,
                        icon: const Icon(Icons.search),
                        label: const Text('بحث'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF25D366),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _clearSearch,
                        icon: const Icon(Icons.clear),
                        label: const Text('مسح'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white54),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // النتائج
          Expanded(
            child: _isSearching
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF25D366),
                    ),
                  )
                : _searchResults.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد نتائج\nجرب تعديل معايير البحث',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white54,
                            fontSize: 16,
                          ),
                        ),
                      )
                    : Column(
                        children: [
                          // عدد النتائج
                          Container(
                            padding: const EdgeInsets.all(12),
                            color: const Color(0xFF1F2C34),
                            child: Row(
                              children: [
                                const Icon(Icons.search, color: Color(0xFF25D366)),
                                const SizedBox(width: 8),
                                Text(
                                  'تم العثور على ${_searchResults.length} نتيجة',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // قائمة النتائج
                          Expanded(
                            child: ListView.builder(
                              itemCount: _searchResults.length,
                              itemBuilder: (context, index) {
                                final message = _searchResults[index];
                                return MessageBubble(
                                  message: message,
                                  isMe: false, // عرض جميع النتائج على اليسار للوضوح
                                  showDateHeader: index == 0 || 
                                      !_isSameDay(_searchResults[index - 1].timestamp, message.timestamp),
                                  onLongPress: () => _showMessageOptions(message),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF25D366),
              surface: Color(0xFF1F2C34),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF25D366),
              surface: Color(0xFF1F2C34),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _performSearch() {
    setState(() {
      _isSearching = true;
    });

    // تأخير بسيط لإظهار مؤشر التحميل
    Future.delayed(const Duration(milliseconds: 500), () {
      final results = widget.messages.where((message) {
        // البحث النصي
        if (_searchController.text.isNotEmpty) {
          if (!message.content.toLowerCase().contains(_searchController.text.toLowerCase())) {
            return false;
          }
        }

        // البحث بالمرسل
        if (_senderController.text.isNotEmpty) {
          if (!message.sender.toLowerCase().contains(_senderController.text.toLowerCase())) {
            return false;
          }
        }

        // فلترة نوع الرسالة
        if (_selectedType != 'all' && message.type != _selectedType) {
          return false;
        }

        // فلترة التاريخ
        if (_startDate != null) {
          if (message.timestamp.isBefore(_startDate!)) {
            return false;
          }
        }

        if (_endDate != null) {
          final endOfDay = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
          if (message.timestamp.isAfter(endOfDay)) {
            return false;
          }
        }

        return true;
      }).toList();

      // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
      results.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _senderController.clear();
      _startDate = null;
      _endDate = null;
      _selectedType = 'all';
      _searchResults = [];
    });
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  void _showMessageOptions(ChatMessage message) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1F2C34),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility, color: Colors.blue),
              title: const Text('الانتقال للرسالة في المحادثة', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(message); // إرجاع الرسالة للشاشة الرئيسية
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy, color: Colors.green),
              title: const Text('نسخ المحتوى', style: TextStyle(color: Colors.white)),
              onTap: () {
                // نسخ محتوى الرسالة
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _senderController.dispose();
    super.dispose();
  }
}
