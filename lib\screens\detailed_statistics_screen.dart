import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/chat.dart';

import '../widgets/safe_area_wrapper.dart';

class DetailedStatisticsScreen extends StatefulWidget {
  final Chat chat;

  const DetailedStatisticsScreen({
    super.key,
    required this.chat,
  });

  @override
  State<DetailedStatisticsScreen> createState() => _DetailedStatisticsScreenState();
}

class _DetailedStatisticsScreenState extends State<DetailedStatisticsScreen> {
  late Map<String, dynamic> statistics;

  @override
  void initState() {
    super.initState();
    _calculateStatistics();
  }

  void _calculateStatistics() {
    final messages = widget.chat.messages;
    
    // إحصائيات أساسية
    final senderCounts = <String, int>{};
    final typeCounts = <String, int>{};
    final dailyCounts = <String, int>{};
    final hourlyCounts = <int, int>{};
    final monthlyCounts = <String, int>{};
    
    // إحصائيات متقدمة
    int totalWords = 0;
    int totalCharacters = 0;
    final wordFrequency = <String, int>{};
    final emojiCounts = <String, int>{};
    
    // تحليل الرسائل
    for (final message in messages) {
      // عد المرسلين
      senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
      
      // عد الأنواع
      typeCounts[message.type] = (typeCounts[message.type] ?? 0) + 1;
      
      // عد يومي
      final dateKey = DateFormat('yyyy-MM-dd').format(message.timestamp);
      dailyCounts[dateKey] = (dailyCounts[dateKey] ?? 0) + 1;
      
      // عد ساعي
      final hour = message.timestamp.hour;
      hourlyCounts[hour] = (hourlyCounts[hour] ?? 0) + 1;
      
      // عد شهري
      final monthKey = DateFormat('yyyy-MM').format(message.timestamp);
      monthlyCounts[monthKey] = (monthlyCounts[monthKey] ?? 0) + 1;
      
      // تحليل النص
      if (message.type == 'text') {
        final text = message.content;
        totalCharacters += text.length;
        
        // عد الكلمات
        final words = text.split(RegExp(r'\s+'));
        totalWords += words.length;
        
        // تكرار الكلمات
        for (final word in words) {
          final cleanWord = word.toLowerCase().replaceAll(RegExp(r'[^\u0600-\u06FFa-zA-Z]'), '');
          if (cleanWord.length > 2) {
            wordFrequency[cleanWord] = (wordFrequency[cleanWord] ?? 0) + 1;
          }
        }
        
        // عد الإيموجي
        final emojiRegex = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
        final emojis = emojiRegex.allMatches(text);
        for (final match in emojis) {
          final emoji = match.group(0)!;
          emojiCounts[emoji] = (emojiCounts[emoji] ?? 0) + 1;
        }
      }
    }
    
    // ترتيب البيانات
    final topSenders = senderCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final topWords = wordFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value))
      ..take(10).toList();
    
    final topEmojis = emojiCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value))
      ..take(10).toList();
    
    // حفظ الإحصائيات
    statistics = {
      'totalMessages': messages.length,
      'totalWords': totalWords,
      'totalCharacters': totalCharacters,
      'averageWordsPerMessage': totalWords > 0 ? (totalWords / messages.where((m) => m.type == 'text').length).toStringAsFixed(1) : '0',
      'averageCharsPerMessage': totalCharacters > 0 ? (totalCharacters / messages.where((m) => m.type == 'text').length).toStringAsFixed(1) : '0',
      'senderCounts': senderCounts,
      'typeCounts': typeCounts,
      'dailyCounts': dailyCounts,
      'hourlyCounts': hourlyCounts,
      'monthlyCounts': monthlyCounts,
      'topSenders': topSenders,
      'topWords': topWords,
      'topEmojis': topEmojis,
      'dateRange': '${DateFormat('dd/MM/yyyy').format(widget.chat.startDate)} - ${DateFormat('dd/MM/yyyy').format(widget.chat.endDate)}',
      'duration': widget.chat.endDate.difference(widget.chat.startDate).inDays,
    };
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      backgroundColor: const Color(0xFF0C1317),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2F32),
        title: Text(
          '📊 إحصائيات: ${widget.chat.name}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      extraBottomPadding: 32.0, // مساحة إضافية للأسفل
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildOverviewCard(),
          const SizedBox(height: 16),
          _buildSendersChart(),
          const SizedBox(height: 16),
          _buildMessageTypesChart(),
          const SizedBox(height: 16),
          _buildHourlyActivityChart(),
          const SizedBox(height: 16),
          _buildTopWordsCard(),
          const SizedBox(height: 16),
          _buildTopEmojisCard(),
          const SizedBox(height: 16),
          _buildTimelineCard(),
        ],
      ),
    );
  }

  Widget _buildOverviewCard() {
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'نظرة عامة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatRow('📝 إجمالي الرسائل', '${statistics['totalMessages']}'),
            _buildStatRow('📅 فترة المحادثة', '${statistics['duration']} يوم'),
            _buildStatRow('📆 التاريخ', statistics['dateRange']),
            _buildStatRow('🔤 إجمالي الكلمات', '${statistics['totalWords']}'),
            _buildStatRow('📏 إجمالي الأحرف', '${statistics['totalCharacters']}'),
            _buildStatRow('📊 متوسط الكلمات/رسالة', statistics['averageWordsPerMessage']),
            _buildStatRow('📈 متوسط الأحرف/رسالة', statistics['averageCharsPerMessage']),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSendersChart() {
    final senderCounts = statistics['senderCounts'] as Map<String, int>;
    if (senderCounts.isEmpty) return const SizedBox();

    final sections = senderCounts.entries.map((entry) {
      final percentage = (entry.value / statistics['totalMessages'] * 100);
      return PieChartSectionData(
        color: _getColorForSender(entry.key),
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.people, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'توزيع الرسائل حسب المرسل',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            const SizedBox(height: 16),
            ...senderCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getColorForSender(entry.key),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ),
                  Text(
                    '${entry.value} رسالة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Color _getColorForSender(String sender) {
    final colors = [
      const Color(0xFF25D366),
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[sender.hashCode % colors.length];
  }

  Widget _buildMessageTypesChart() {
    final typeCounts = statistics['typeCounts'] as Map<String, int>;
    if (typeCounts.isEmpty) return const SizedBox();

    final typeNames = {
      'text': '📝 نص',
      'image': '📷 صورة',
      'video': '🎥 فيديو',
      'audio': '🎧 صوت',
      'document': '📄 مستند',
      'link': '🔗 رابط',
    };

    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.category, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'أنواع الرسائل',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...typeCounts.entries.map((entry) {
              final percentage = (entry.value / statistics['totalMessages'] * 100);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          typeNames[entry.key] ?? entry.key,
                          style: const TextStyle(color: Colors.white70),
                        ),
                        Text(
                          '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: percentage / 100,
                      backgroundColor: Colors.grey[700],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _getColorForType(entry.key),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Color _getColorForType(String type) {
    switch (type) {
      case 'text': return const Color(0xFF25D366);
      case 'image': return Colors.blue;
      case 'video': return Colors.red;
      case 'audio': return Colors.orange;
      case 'document': return Colors.purple;
      case 'link': return Colors.teal;
      default: return Colors.grey;
    }
  }

  Widget _buildHourlyActivityChart() {
    final hourlyCounts = statistics['hourlyCounts'] as Map<int, int>;
    if (hourlyCounts.isEmpty) return const SizedBox();

    final spots = <FlSpot>[];
    for (int hour = 0; hour < 24; hour++) {
      spots.add(FlSpot(hour.toDouble(), (hourlyCounts[hour] ?? 0).toDouble()));
    }

    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.schedule, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'النشاط حسب الساعة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey[700]!,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 4,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '${value.toInt()}:00',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 10,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 10,
                            ),
                          );
                        },
                        reservedSize: 32,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: Colors.grey[700]!),
                  ),
                  minX: 0,
                  maxX: 23,
                  minY: 0,
                  maxY: spots.map((e) => e.y).reduce((a, b) => a > b ? a : b) * 1.1,
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      gradient: const LinearGradient(
                        colors: [Color(0xFF25D366), Colors.blue],
                      ),
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF25D366).withValues(alpha: 0.3),
                            Colors.blue.withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopWordsCard() {
    final topWords = statistics['topWords'] as List<MapEntry<String, int>>;
    if (topWords.isEmpty) return const SizedBox();

    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.text_fields, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'أكثر الكلمات استخداماً',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...topWords.take(10).map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      entry.key,
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${entry.value}',
                      style: const TextStyle(
                        color: Color(0xFF25D366),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildTopEmojisCard() {
    final topEmojis = statistics['topEmojis'] as List<MapEntry<String, int>>;
    if (topEmojis.isEmpty) return const SizedBox();

    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.emoji_emotions, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'أكثر الإيموجي استخداماً',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: topEmojis.take(20).map((entry) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      entry.key,
                      style: const TextStyle(fontSize: 20),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${entry.value}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard() {
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.timeline, color: Color(0xFF25D366)),
                SizedBox(width: 8),
                Text(
                  'الخط الزمني',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatRow('📅 تاريخ البداية', DateFormat('dd/MM/yyyy HH:mm').format(widget.chat.startDate)),
            _buildStatRow('📅 تاريخ النهاية', DateFormat('dd/MM/yyyy HH:mm').format(widget.chat.endDate)),
            _buildStatRow('⏱️ المدة الإجمالية', '${statistics['duration']} يوم'),
            _buildStatRow('📊 متوسط الرسائل/يوم', '${(statistics['totalMessages'] / (statistics['duration'] + 1)).toStringAsFixed(1)}'),
          ],
        ),
      ),
    );
  }
}
