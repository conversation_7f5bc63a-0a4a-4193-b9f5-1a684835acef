import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';
import '../services/chat_share_service.dart';

/// شاشة إحصائيات المحادثة
class ChatStatisticsScreen extends StatefulWidget {
  final List<ChatMessage> messages;
  final String chatName;
  
  const ChatStatisticsScreen({
    super.key,
    required this.messages,
    required this.chatName,
  });

  @override
  State<ChatStatisticsScreen> createState() => _ChatStatisticsScreenState();
}

class _ChatStatisticsScreenState extends State<ChatStatisticsScreen> {
  Map<String, int> senderCounts = {};
  Map<String, int> typeCounts = {};
  Map<String, int> dailyCounts = {};
  Map<String, int> hourlyCounts = {};
  
  @override
  void initState() {
    super.initState();
    _calculateStatistics();
  }
  
  void _calculateStatistics() {
    senderCounts.clear();
    typeCounts.clear();
    dailyCounts.clear();
    hourlyCounts.clear();
    
    for (final message in widget.messages) {
      // إحصائيات المرسلين
      senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
      
      // إحصائيات أنواع الرسائل
      typeCounts[message.type] = (typeCounts[message.type] ?? 0) + 1;
      
      // إحصائيات يومية
      final dateKey = DateFormat('yyyy-MM-dd').format(message.timestamp);
      dailyCounts[dateKey] = (dailyCounts[dateKey] ?? 0) + 1;
      
      // إحصائيات ساعية
      final hourKey = message.timestamp.hour.toString().padLeft(2, '0');
      hourlyCounts[hourKey] = (hourlyCounts[hourKey] ?? 0) + 1;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0C1317),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2F32),
        title: Text(
          '📊 إحصائيات: ${widget.chatName}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ChatShareService.shareChatStatistics(
                widget.messages, 
                widget.chatName, 
                context,
              );
            },
          ),
        ],
      ),
      body: widget.messages.isEmpty
          ? const Center(
              child: Text(
                '❌ لا توجد رسائل لعرض الإحصائيات',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الإحصائيات العامة
                  _buildGeneralStats(),
                  const SizedBox(height: 24),
                  
                  // إحصائيات المرسلين
                  _buildSenderStats(),
                  const SizedBox(height: 24),
                  
                  // إحصائيات أنواع الرسائل
                  _buildMessageTypeStats(),
                  const SizedBox(height: 24),
                  
                  // إحصائيات ساعية
                  _buildHourlyStats(),
                ],
              ),
            ),
    );
  }
  
  Widget _buildGeneralStats() {
    final firstMessage = widget.messages.isNotEmpty ? widget.messages.first : null;
    final lastMessage = widget.messages.isNotEmpty ? widget.messages.last : null;
    
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📈 الإحصائيات العامة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '💬',
                    'إجمالي الرسائل',
                    widget.messages.length.toString(),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '👥',
                    'المشاركون',
                    senderCounts.length.toString(),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (firstMessage != null && lastMessage != null) ...[
              _buildStatItem(
                '📅',
                'فترة المحادثة',
                '${DateFormat('dd/MM/yyyy').format(firstMessage.timestamp)} - ${DateFormat('dd/MM/yyyy').format(lastMessage.timestamp)}',
              ),
              const SizedBox(height: 8),
              _buildStatItem(
                '⏱️',
                'مدة المحادثة',
                '${lastMessage.timestamp.difference(firstMessage.timestamp).inDays} يوم',
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildSenderStats() {
    final sortedSenders = senderCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🏆 أكثر المرسلين نشاطاً',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...sortedSenders.take(5).map((entry) {
              final percentage = (entry.value / widget.messages.length * 100);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            entry.key,
                            style: const TextStyle(color: Colors.white),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: percentage / 100,
                      backgroundColor: Colors.grey[700],
                      valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF25D366)),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMessageTypeStats() {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
    ];
    
    final pieData = typeCounts.entries.map((entry) {
      String typeName = '';
      switch (entry.key) {
        case 'text':
          typeName = 'نصية';
          break;
        case 'image':
          typeName = 'صور';
          break;
        case 'video':
          typeName = 'فيديو';
          break;
        case 'audio':
          typeName = 'صوتية';
          break;
        case 'document':
          typeName = 'مستندات';
          break;
        case 'call':
        case 'unknown_call':
          typeName = 'مكالمات';
          break;
        default:
          typeName = 'أخرى';
      }
      
      return PieChartSectionData(
        value: entry.value.toDouble(),
        title: '${(entry.value / widget.messages.length * 100).toStringAsFixed(1)}%',
        color: colors[typeCounts.keys.toList().indexOf(entry.key) % colors.length],
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
    
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📝 أنواع الرسائل',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: pieData,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // وسيلة الإيضاح
            Wrap(
              children: typeCounts.entries.map((entry) {
                String typeName = '';
                String icon = '';
                switch (entry.key) {
                  case 'text':
                    typeName = 'نصية';
                    icon = '💬';
                    break;
                  case 'image':
                    typeName = 'صور';
                    icon = '📷';
                    break;
                  case 'video':
                    typeName = 'فيديو';
                    icon = '🎥';
                    break;
                  case 'audio':
                    typeName = 'صوتية';
                    icon = '🎧';
                    break;
                  case 'document':
                    typeName = 'مستندات';
                    icon = '📄';
                    break;
                  case 'call':
                  case 'unknown_call':
                    typeName = 'مكالمات';
                    icon = '📞';
                    break;
                  default:
                    typeName = 'أخرى';
                    icon = '❓';
                }
                
                final color = colors[typeCounts.keys.toList().indexOf(entry.key) % colors.length];
                
                return Padding(
                  padding: const EdgeInsets.all(4),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$icon $typeName (${entry.value})',
                        style: const TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHourlyStats() {
    final hourlyData = List.generate(24, (index) {
      final hour = index.toString().padLeft(2, '0');
      return FlSpot(index.toDouble(), (hourlyCounts[hour] ?? 0).toDouble());
    });
    
    return Card(
      color: const Color(0xFF2A2F32),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '⏰ النشاط حسب الساعة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: 4,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toInt()}:00',
                            style: const TextStyle(color: Colors.white70, fontSize: 10),
                          );
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: hourlyData,
                      isCurved: true,
                      color: const Color(0xFF25D366),
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: const Color(0xFF25D366).withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatItem(String icon, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(icon, style: const TextStyle(fontSize: 16)),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
