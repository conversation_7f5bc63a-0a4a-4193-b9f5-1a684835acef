import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat.dart';
import '../models/chat_message.dart';

class DatabaseService {
  static const String _chatsKey = 'whatsapp_chats';
  static const String _messagesPrefix = 'whatsapp_messages_';

  // إدراج محادثة جديدة
  static Future<void> insertChat(Chat chat) async {
    final prefs = await SharedPreferences.getInstance();

    // جلب المحادثات الحالية
    final chats = await getAllChats();

    // إضافة المحادثة الجديدة أو تحديثها
    final existingIndex = chats.indexWhere((c) => c.id == chat.id);
    if (existingIndex >= 0) {
      chats[existingIndex] = chat;
    } else {
      chats.add(chat);
    }

    // حفظ المحادثات
    final chatsJson = chats.map((c) => c.toMap()).toList();
    await prefs.setString(_chats<PERSON><PERSON>, json<PERSON><PERSON>de(chatsJson));
  }

  // إدراج رسائل المحادثة
  static Future<void> insertMessages(String chatId, List<ChatMessage> messages) async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = messages.map((m) => m.toMap()).toList();
    await prefs.setString('$_messagesPrefix$chatId', jsonEncode(messagesJson));
  }

  // تحديث رسائل المحادثة (لميزة تغيير الأسماء)
  static Future<void> updateMessages(String chatId, List<ChatMessage> messages) async {
    // نفس العملية كـ insertMessages لأننا نستخدم SharedPreferences
    await insertMessages(chatId, messages);
  }

  // جلب جميع المحادثات
  static Future<List<Chat>> getAllChats() async {
    final prefs = await SharedPreferences.getInstance();
    final chatsString = prefs.getString(_chatsKey);

    if (chatsString == null) return [];

    try {
      final List<dynamic> chatsJson = jsonDecode(chatsString);
      return chatsJson.map((json) => Chat.fromMap(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // جلب رسائل محادثة معينة
  static Future<List<ChatMessage>> getChatMessages(String chatId) async {
    final prefs = await SharedPreferences.getInstance();
    final messagesString = prefs.getString('$_messagesPrefix$chatId');

    if (messagesString == null) return [];

    try {
      final List<dynamic> messagesJson = jsonDecode(messagesString);
      return messagesJson.map((json) => ChatMessage.fromMap(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // البحث في الرسائل
  static Future<List<ChatMessage>> searchMessages(String chatId, String query) async {
    final messages = await getChatMessages(chatId);
    return messages
        .where((message) => message.content.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  // حذف محادثة
  static Future<void> deleteChat(String chatId) async {
    final prefs = await SharedPreferences.getInstance();

    // حذف المحادثة من القائمة
    final chats = await getAllChats();
    chats.removeWhere((chat) => chat.id == chatId);

    final chatsJson = chats.map((c) => c.toMap()).toList();
    await prefs.setString(_chatsKey, jsonEncode(chatsJson));

    // حذف رسائل المحادثة
    await prefs.remove('$_messagesPrefix$chatId');
  }

  // تحديث اسم المحادثة
  static Future<void> updateChatName(String chatId, String newName) async {
    final prefs = await SharedPreferences.getInstance();

    // جلب المحادثات الحالية
    final chats = await getAllChats();

    // العثور على المحادثة وتحديث اسمها
    final chatIndex = chats.indexWhere((chat) => chat.id == chatId);
    if (chatIndex >= 0) {
      final chat = chats[chatIndex];
      final updatedChat = Chat(
        id: chat.id,
        name: newName,
        startDate: chat.startDate,
        endDate: chat.endDate,
        folderPath: chat.folderPath,
        messages: chat.messages,
      );
      chats[chatIndex] = updatedChat;

      // حفظ المحادثات المحدثة
      final chatsJson = chats.map((c) => c.toMap()).toList();
      await prefs.setString(_chatsKey, jsonEncode(chatsJson));
    }
  }
}
