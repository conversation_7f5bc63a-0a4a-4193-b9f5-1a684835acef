import 'package:flutter/material.dart';
import 'dart:io';
import '../models/chat_message.dart';
import 'media_gallery_widget.dart';
import 'audio_player_widget.dart';
import 'video_thumbnail_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;

/// شريط التبويبات للوسائط والمستندات والروابط
class MediaTabsWidget extends StatefulWidget {
  final List<ChatMessage> messages;
  final String chatName;
  
  const MediaTabsWidget({
    super.key,
    required this.messages,
    required this.chatName,
  });

  @override
  State<MediaTabsWidget> createState() => _MediaTabsWidgetState();
}

class _MediaTabsWidgetState extends State<MediaTabsWidget> with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<ChatMessage> images = [];
  List<ChatMessage> videos = [];
  List<ChatMessage> audios = [];
  List<ChatMessage> documents = [];
  List<ChatMessage> links = [];
  List<ChatMessage> others = [];
  
  @override
  void initState() {
    super.initState();
    _categorizeMessages();
    _tabController = TabController(length: 6, vsync: this);
  }
  
  void _categorizeMessages() {
    debugPrint('📊 بدء تصنيف ${widget.messages.length} رسالة...');

    // أولاً، اجمع جميع الملفات الموجودة فعلياً في مجلد المحادثة
    final availableFiles = _scanAvailableFiles();
    debugPrint('📁 الملفات المتاحة: ${availableFiles.length}');

    for (final message in widget.messages) {
      debugPrint('🔍 تصنيف رسالة: نوع=${message.type}, محتوى=${message.content.length > 50 ? message.content.substring(0, 50) + "..." : message.content}, مسار=${message.filePath}');

      switch (message.type) {
        case 'image':
          images.add(message);
          debugPrint('📷 أضيفت صورة: ${message.content}');
          break;
        case 'video':
          videos.add(message);
          debugPrint('🎥 أضيف فيديو: ${message.content}');
          break;
        case 'audio':
          audios.add(message);
          debugPrint('🎧 أضيف صوت: ${message.content}');
          break;
        case 'document':
          // تصنيف المستندات حسب النوع
          final fileName = message.content.toLowerCase();
          final isDocument = _isDocumentFile(fileName);

          if (isDocument) {
            documents.add(message);
            debugPrint('📄 أضيف مستند: ${message.content}');
          } else {
            others.add(message);
            debugPrint('🧩 أضيف ملف آخر: ${message.content}');
          }
          break;
        case 'link':
          links.add(message);
          debugPrint('🔗 أضيف رابط: ${message.content}');
          break;
        default:
          // تجاهل الرسائل النصية والمكالمات والإيموجي
          if (message.type != 'text' &&
              message.type != 'call' &&
              message.type != 'unknown_call' &&
              message.type != 'emoji' &&
              !_isEmojiOnly(message.content)) {
            others.add(message);
            debugPrint('🧩 أضيف نوع آخر: ${message.type} - ${message.content}');
          }
      }
    }

    // إضافة الملفات المتاحة التي لم تُربط برسائل
    _addUnlinkedFiles(availableFiles);

    debugPrint('📊 نتائج التصنيف:');
    debugPrint('   📷 صور: ${images.length}');
    debugPrint('   🎥 فيديوهات: ${videos.length}');
    debugPrint('   🎧 صوتيات: ${audios.length}');
    debugPrint('   📄 مستندات: ${documents.length}');
    debugPrint('   🔗 روابط: ${links.length}');
    debugPrint('   🧩 أخرى: ${others.length}');
  }

  /// تحديد ما إذا كان الملف مستنداً أم لا
  bool _isDocumentFile(String fileName) {
    final documentExtensions = [
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
      'txt', 'rtf', 'csv', 'odt', 'ods', 'odp',
      'zip', 'rar', '7z', 'apk', 'exe', 'msi', 'deb', 'rpm'
    ];

    final extension = path.extension(fileName).toLowerCase().replaceFirst('.', '');
    final isDocument = documentExtensions.contains(extension);

    debugPrint('🔍 فحص ملف: $fileName, امتداد: $extension, مستند: $isDocument');
    return isDocument;
  }

  /// تحديد ما إذا كان النص يحتوي على إيموجي فقط
  bool _isEmojiOnly(String text) {
    if (text.trim().isEmpty) return false;

    // إزالة المسافات والأرقام والنقاط
    final cleanText = text.replaceAll(RegExp(r'[\s\d\.\-\:\،]'), '');

    // إذا كان النص فارغ بعد إزالة المسافات والأرقام، فهو ليس إيموجي
    if (cleanText.isEmpty) return false;

    // إذا كان النص قصير جداً (أقل من 5 أحرف) وليس فيه أحرف عربية أو إنجليزية
    if (cleanText.length <= 4 && !RegExp(r'[a-zA-Zأ-ي]').hasMatch(cleanText)) {
      return true;
    }

    return false;
  }

  /// فحص جميع الملفات المتاحة في مجلد المحادثة
  List<String> _scanAvailableFiles() {
    final availableFiles = <String>[];

    try {
      // الحصول على مجلد المحادثة من أول رسالة لها مسار ملف
      String? chatFolderPath;
      for (final message in widget.messages) {
        if (message.filePath != null) {
          chatFolderPath = path.dirname(message.filePath!);
          break;
        }
      }

      if (chatFolderPath == null) {
        debugPrint('❌ لم يتم العثور على مجلد المحادثة');
        return availableFiles;
      }

      debugPrint('📂 فحص مجلد المحادثة: $chatFolderPath');
      final chatDir = Directory(chatFolderPath);

      if (!chatDir.existsSync()) {
        debugPrint('❌ مجلد المحادثة غير موجود: $chatFolderPath');
        return availableFiles;
      }

      // فحص جميع الملفات في المجلد
      final files = chatDir.listSync().whereType<File>();
      for (final file in files) {
        final fileName = path.basename(file.path);
        if (!fileName.endsWith('.txt')) { // تجاهل ملف النص
          availableFiles.add(file.path);
          debugPrint('📄 ملف متاح: $fileName');
        }
      }

    } catch (e) {
      debugPrint('❌ خطأ في فحص الملفات المتاحة: $e');
    }

    return availableFiles;
  }

  /// إضافة الملفات غير المربوطة برسائل
  void _addUnlinkedFiles(List<String> availableFiles) {
    final linkedFiles = <String>{};

    // جمع جميع الملفات المربوطة برسائل
    for (final message in widget.messages) {
      if (message.filePath != null) {
        linkedFiles.add(message.filePath!);
      }
    }

    debugPrint('📎 ملفات مربوطة: ${linkedFiles.length}');
    debugPrint('📁 ملفات متاحة: ${availableFiles.length}');

    // إضافة الملفات غير المربوطة
    for (final filePath in availableFiles) {
      if (!linkedFiles.contains(filePath)) {
        final fileName = path.basename(filePath);
        final extension = path.extension(fileName).toLowerCase().replaceFirst('.', '');

        debugPrint('🔗 إضافة ملف غير مربوط: $fileName');

        // تحديد نوع الملف وإضافته للقائمة المناسبة
        final fakeMessage = ChatMessage(
          timestamp: DateTime.now(),
          sender: 'نظام',
          type: _determineFileType(extension),
          content: fileName,
          filePath: filePath,
        );

        switch (fakeMessage.type) {
          case 'image':
            images.add(fakeMessage);
            break;
          case 'video':
            videos.add(fakeMessage);
            break;
          case 'audio':
            audios.add(fakeMessage);
            break;
          case 'document':
            if (_isDocumentFile(fileName)) {
              documents.add(fakeMessage);
            } else {
              others.add(fakeMessage);
            }
            break;
          default:
            others.add(fakeMessage);
        }
      }
    }
  }

  /// تحديد نوع الملف حسب الامتداد
  String _determineFileType(String extension) {
    // صور
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg', 'heic', 'heif'].contains(extension)) {
      return 'image';
    }

    // فيديوهات
    if (['mp4', 'mov', 'avi', 'mkv', '3gp', 'webm', 'flv', 'wmv', 'm4v', 'mpg', 'mpeg'].contains(extension)) {
      return 'video';
    }

    // صوتيات
    if (['opus', 'mp3', 'm4a', 'wav', 'aac', 'ogg', 'wma', 'flac', 'amr'].contains(extension)) {
      return 'audio';
    }

    // مستندات
    if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'zip', 'rar', '7z', 'apk'].contains(extension)) {
      return 'document';
    }

    return 'document'; // افتراضي للملفات غير المعروفة
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0C1317),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2F32),
        title: Text(
          '📎 وسائط: ${widget.chatName}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: const Color(0xFF25D366),
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.image),
              text: '📷 الصور (${images.length})',
            ),
            Tab(
              icon: const Icon(Icons.videocam),
              text: '🎥 الفيديو (${videos.length})',
            ),
            Tab(
              icon: const Icon(Icons.audiotrack),
              text: '🎧 الصوتيات (${audios.length})',
            ),
            Tab(
              icon: const Icon(Icons.description),
              text: '📄 المستندات (${documents.length})',
            ),
            Tab(
              icon: const Icon(Icons.link),
              text: '🔗 الروابط (${links.length})',
            ),
            Tab(
              icon: const Icon(Icons.category),
              text: '🧩 أخرى (${others.length})',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildImagesTab(),
          _buildVideosTab(),
          _buildAudiosTab(),
          _buildDocumentsTab(),
          _buildLinksTab(),
          _buildOthersTab(),
        ],
      ),
    );
  }
  
  Widget _buildImagesTab() {
    if (images.isEmpty) {
      return const Center(
        child: Text(
          '📷 لا توجد صور في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }
    
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final message = images[index];
        return GestureDetector(
          onTap: () {
            debugPrint('🖼️ الضغط على صورة في شريط الوسائط: ${message.filePath}');
            final imagePaths = images
                .where((m) => m.filePath != null && File(m.filePath!).existsSync())
                .map((m) => m.filePath!)
                .toList();

            debugPrint('🖼️ عدد الصور المتاحة: ${imagePaths.length}');

            if (imagePaths.isNotEmpty) {
              debugPrint('🖼️ فتح معرض الصور...');
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => MediaGalleryWidget(
                    mediaPaths: imagePaths,
                    initialIndex: imagePaths.indexOf(message.filePath!),
                    mediaTypes: List.filled(imagePaths.length, 'image'),
                  ),
                ),
              );
            } else {
              debugPrint('❌ لا توجد صور متاحة للعرض');
            }
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[800],
            ),
            child: message.filePath != null && File(message.filePath!).existsSync()
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(message.filePath!),
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 48,
                  ),
          ),
        );
      },
    );
  }
  
  Widget _buildVideosTab() {
    if (videos.isEmpty) {
      return const Center(
        child: Text(
          '🎥 لا توجد فيديوهات في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 16 / 9,
      ),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final message = videos[index];
        if (message.filePath != null && File(message.filePath!).existsSync()) {
          return VideoPreviewWidget(
            videoPath: message.filePath!,
            width: double.infinity,
            height: double.infinity,
            showPlayButton: false, // إزالة زر التشغيل في قسم الوسائط
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => MediaGalleryWidget(
                    mediaPaths: [message.filePath!],
                    initialIndex: 0,
                    mediaTypes: ['video'],
                  ),
                ),
              );
            },
          );
        } else {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[800],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.black26,
                  ),
                  child: const Icon(
                    Icons.videocam,
                    color: Colors.white,
                    size: 48,
                  ),
                ),
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 32,
                ),
                Positioned(
                  bottom: 8,
                  left: 8,
                  right: 8,
                  child: Text(
                    'ملف غير موجود',
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      shadows: [
                        Shadow(
                          color: Colors.black,
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }
  
  Widget _buildAudiosTab() {
    if (audios.isEmpty) {
      return const Center(
        child: Text(
          '🎧 لا توجد ملفات صوتية في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: audios.length,
      itemBuilder: (context, index) {
        final message = audios[index];
        return Card(
          color: const Color(0xFF2A2F32),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: message.filePath != null && File(message.filePath!).existsSync()
              ? SimpleAudioPlayer(
                  audioPath: message.filePath!,
                  fileName: message.content.split('/').last,
                )
              : ListTile(
                  leading: const Icon(Icons.audiotrack, color: Colors.grey),
                  title: Text(
                    message.content.split('/').last,
                    style: const TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    '❌ ملف غير موجود',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
        );
      },
    );
  }
  
  Widget _buildDocumentsTab() {
    if (documents.isEmpty) {
      return const Center(
        child: Text(
          '📄 لا توجد مستندات في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        final message = documents[index];
        return Card(
          color: const Color(0xFF2A2F32),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: const Icon(Icons.description, color: Color(0xFF25D366)),
            title: Text(
              message.content.split('/').last,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              message.filePath != null && File(message.filePath!).existsSync()
                  ? '📄 مستند'
                  : '❌ ملف غير موجود',
              style: TextStyle(
                color: message.filePath != null && File(message.filePath!).existsSync()
                    ? Colors.white70
                    : Colors.red,
              ),
            ),
            trailing: const Icon(Icons.open_in_new, color: Colors.white70),
            onTap: () async {
              debugPrint('📄 محاولة فتح مستند: ${message.filePath}');

              if (message.filePath != null && File(message.filePath!).existsSync()) {
                try {
                  // استخدام OpenFile لفتح المستند
                  final result = await OpenFile.open(message.filePath!);
                  debugPrint('📄 نتيجة فتح المستند: ${result.message}');

                  if (result.type != ResultType.done) {
                    throw Exception(result.message);
                  }
                } catch (e) {
                  debugPrint('❌ خطأ في فتح المستند: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('❌ خطأ في فتح المستند: $e'),
                        action: SnackBarAction(
                          label: 'نسخ المسار',
                          onPressed: () {
                            // يمكن إضافة نسخ المسار هنا لاحقاً
                          },
                        ),
                      ),
                    );
                  }
                }
              } else {
                debugPrint('❌ ملف المستند غير موجود: ${message.filePath}');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('❌ ملف المستند غير موجود: ${message.content}'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              }
            },
          ),
        );
      },
    );
  }
  
  Widget _buildLinksTab() {
    if (links.isEmpty) {
      return const Center(
        child: Text(
          '🔗 لا توجد روابط في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: links.length,
      itemBuilder: (context, index) {
        final message = links[index];
        return Card(
          color: const Color(0xFF2A2F32),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: const Icon(Icons.link, color: Colors.blue),
            title: Text(
              message.content,
              style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: const Text(
              '🔗 اضغط للفتح في المتصفح',
              style: TextStyle(color: Colors.white70),
            ),
            trailing: const Icon(Icons.open_in_new, color: Colors.blue),
            onTap: () async {
              try {
                final uri = Uri.parse(message.content);
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                } else {
                  throw Exception('لا يمكن فتح هذا الرابط');
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('❌ خطأ في فتح الرابط: $e')),
                  );
                }
              }
            },
          ),
        );
      },
    );
  }
  
  Widget _buildOthersTab() {
    if (others.isEmpty) {
      return const Center(
        child: Text(
          '🧩 لا توجد ملفات أخرى في هذه المحادثة',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: others.length,
      itemBuilder: (context, index) {
        final message = others[index];
        final fileName = message.content.split('/').last;
        final fileExtension = path.extension(fileName).toLowerCase().replaceFirst('.', '');

        // تحديد أيقونة الملف حسب النوع
        IconData fileIcon;
        Color iconColor;

        switch (fileExtension) {
          case 'zip':
          case 'rar':
          case '7z':
            fileIcon = Icons.archive;
            iconColor = Colors.amber;
            break;
          case 'apk':
            fileIcon = Icons.android;
            iconColor = Colors.green;
            break;
          case 'exe':
          case 'msi':
            fileIcon = Icons.computer;
            iconColor = Colors.blue;
            break;
          default:
            fileIcon = Icons.insert_drive_file;
            iconColor = Colors.orange;
        }

        return Card(
          color: const Color(0xFF2A2F32),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(fileIcon, color: iconColor),
            title: Text(
              fileName,
              style: const TextStyle(color: Colors.white),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              message.filePath != null && File(message.filePath!).existsSync()
                  ? '📁 ملف $fileExtension - اضغط للفتح'
                  : '❌ ملف غير موجود',
              style: TextStyle(
                color: message.filePath != null && File(message.filePath!).existsSync()
                    ? Colors.white70
                    : Colors.red,
              ),
            ),
            trailing: message.filePath != null && File(message.filePath!).existsSync()
                ? const Icon(Icons.open_in_new, color: Colors.white70)
                : const Icon(Icons.error, color: Colors.red),
            onTap: () async {
              debugPrint('🧩 محاولة فتح ملف آخر: ${message.filePath}');

              if (message.filePath != null && File(message.filePath!).existsSync()) {
                try {
                  // استخدام OpenFile لفتح الملف
                  final result = await OpenFile.open(message.filePath!);
                  debugPrint('🧩 نتيجة فتح الملف: ${result.message}');

                  if (result.type != ResultType.done) {
                    throw Exception(result.message);
                  }
                } catch (e) {
                  debugPrint('❌ خطأ في فتح الملف: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('❌ خطأ في فتح الملف: $e'),
                        action: SnackBarAction(
                          label: 'نسخ المسار',
                          onPressed: () {
                            // يمكن إضافة نسخ المسار هنا لاحقاً
                          },
                        ),
                      ),
                    );
                  }
                }
              } else {
                debugPrint('❌ الملف غير موجود: ${message.filePath}');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('❌ الملف غير موجود: $fileName'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              }
            },
          ),
        );
      },
    );
  }
}
