import 'dart:io';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

/// مشغل الصوت المحسن
class AudioPlayerWidget extends StatefulWidget {
  final String audioPath;
  final bool isMe;
  
  const AudioPlayerWidget({
    super.key,
    required this.audioPath,
    required this.isMe,
  });

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  late AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  
  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _setupAudioPlayer();
  }
  
  void _setupAudioPlayer() {
    // استمع لتغييرات حالة التشغيل
    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
          _isLoading = state == PlayerState.playing && _position == Duration.zero;
        });
      }
    });

    // استمع لتغييرات المدة
    _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() {
          _duration = duration;
        });
      }
    });

    // استمع لتغييرات الموضع
    _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() {
          _position = position;
          _isLoading = false;
        });
      }
    });

    // استمع لانتهاء التشغيل
    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _isPlaying = false;
          _position = Duration.zero;
        });
      }
    });

    // تحميل مدة الملف مسبقاً
    _loadAudioDuration();
  }

  Future<void> _loadAudioDuration() async {
    try {
      if (File(widget.audioPath).existsSync()) {
        // تحميل الملف للحصول على المدة فقط
        await _audioPlayer.setSourceDeviceFile(widget.audioPath);
        // المدة ستأتي من onDurationChanged
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مدة الصوت: $e');
    }
  }
  
  Future<void> _playPause() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        setState(() {
          _isLoading = true;
        });
        
        if (_position == Duration.zero) {
          // تشغيل جديد
          if (File(widget.audioPath).existsSync()) {
            await _audioPlayer.play(DeviceFileSource(widget.audioPath));
          } else {
            throw Exception('الملف الصوتي غير موجود');
          }
        } else {
          // استكمال التشغيل
          await _audioPlayer.resume();
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل الصوت: $e');
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _seek(double value) async {
    if (_duration.inMilliseconds > 0 && value >= 0.0 && value <= 1.0) {
      final position = Duration(milliseconds: (value * _duration.inMilliseconds).round());
      // تحديث الموضع مؤقتاً لمنع القفز
      setState(() {
        _position = position;
      });
      await _audioPlayer.seek(position);
    }
  }
  
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: _playPause,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.isMe 
                    ? Colors.black.withValues(alpha: 0.1)
                    : Colors.white.withValues(alpha: 0.1),
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.isMe ? Colors.black : Colors.white,
                        ),
                      ),
                    )
                  : Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: widget.isMe ? Colors.black : Colors.white,
                      size: 24,
                    ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // شريط التقدم والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // شريط التقدم
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                    activeTrackColor: widget.isMe 
                        ? Colors.black.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    inactiveTrackColor: widget.isMe 
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.white.withValues(alpha: 0.3),
                    thumbColor: widget.isMe ? Colors.black : Colors.white,
                  ),
                  child: Slider(
                    value: _duration.inMilliseconds > 0 
                        ? _position.inMilliseconds / _duration.inMilliseconds 
                        : 0.0,
                    onChanged: _duration.inMilliseconds > 0 ? _seek : null,
                  ),
                ),
                
                // الوقت
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_position),
                      style: TextStyle(
                        fontSize: 11,
                        color: widget.isMe 
                            ? Colors.black.withValues(alpha: 0.6)
                            : Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                    Text(
                      _duration.inMilliseconds > 0
                          ? _formatDuration(_duration)
                          : '--:--',
                      style: TextStyle(
                        fontSize: 11,
                        color: widget.isMe
                            ? Colors.black.withValues(alpha: 0.6)
                            : Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 8),
          
          // أيقونة الصوت
          Icon(
            Icons.mic,
            color: widget.isMe 
                ? Colors.black.withValues(alpha: 0.6)
                : Colors.white.withValues(alpha: 0.6),
            size: 16,
          ),
        ],
      ),
    );
  }
}

/// مشغل صوت مبسط للمعاينة
class SimpleAudioPlayer extends StatefulWidget {
  final String audioPath;
  final String fileName;
  
  const SimpleAudioPlayer({
    super.key,
    required this.audioPath,
    required this.fileName,
  });

  @override
  State<SimpleAudioPlayer> createState() => _SimpleAudioPlayerState();
}

class _SimpleAudioPlayerState extends State<SimpleAudioPlayer> {
  late AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  
  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    
    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
        });
      }
    });
    
    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    });
  }
  
  Future<void> _playPause() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        if (File(widget.audioPath).existsSync()) {
          await _audioPlayer.play(DeviceFileSource(widget.audioPath));
        } else {
          throw Exception('الملف الصوتي غير موجود');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل الصوت: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: GestureDetector(
        onTap: _playPause,
        child: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.blue.withValues(alpha: 0.1),
          ),
          child: Icon(
            _isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.blue,
            size: 24,
          ),
        ),
      ),
      title: Text(
        widget.fileName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: const Text('🎧 ملف صوتي'),
      trailing: const Icon(Icons.audiotrack),
    );
  }
}
