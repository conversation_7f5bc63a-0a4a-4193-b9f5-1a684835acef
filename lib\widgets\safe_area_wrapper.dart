import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget لرفع المحتوى عن شريط المهام في أندرويد
class SafeAreaWrapper extends StatelessWidget {
  final Widget child;
  final bool addBottomPadding;
  final double extraBottomPadding;

  const SafeAreaWrapper({
    super.key,
    required this.child,
    this.addBottomPadding = true,
    this.extraBottomPadding = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final bottomPadding = mediaQuery.viewInsets.bottom + 
                         mediaQuery.padding.bottom + 
                         (addBottomPadding ? extraBottomPadding : 0);

    return Padding(
      padding: EdgeInsets.only(
        bottom: bottomPadding,
      ),
      child: child,
    );
  }
}

/// Extension لتطبيق SafeAreaWrapper بسهولة
extension SafeAreaExtension on Widget {
  Widget withSafeArea({
    bool addBottomPadding = true,
    double extraBottomPadding = 16.0,
  }) {
    return SafeAreaWrapper(
      addBottomPadding: addBottomPadding,
      extraBottomPadding: extraBottomPadding,
      child: this,
    );
  }
}

/// Scaffold محسن مع SafeArea تلقائي
class SafeScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? floatingActionButton;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;
  final Widget? bottomNavigationBar;
  final bool extendBody;
  final bool extendBodyBehindAppBar;
  final bool addBottomPadding;
  final double extraBottomPadding;

  const SafeScaffold({
    super.key,
    this.appBar,
    this.body,
    this.floatingActionButton,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.bottomNavigationBar,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
    this.addBottomPadding = true,
    this.extraBottomPadding = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      body: body != null 
          ? SafeAreaWrapper(
              addBottomPadding: addBottomPadding,
              extraBottomPadding: extraBottomPadding,
              child: body!,
            )
          : null,
      floatingActionButton: floatingActionButton,
      drawer: drawer,
      endDrawer: endDrawer,
      backgroundColor: backgroundColor,
      bottomNavigationBar: bottomNavigationBar,
      extendBody: extendBody,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );
  }
}
