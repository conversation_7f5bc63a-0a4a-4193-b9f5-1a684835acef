import 'chat_message.dart';

class Chat {
  final String id;
  final String name;
  final DateTime startDate;
  final DateTime endDate;
  final String folderPath;
  final List<ChatMessage> messages;

  Chat({
    required this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    required this.folderPath,
    required this.messages,
  });

  String get displayName {
    final startDateStr = '${startDate.day.toString().padLeft(2, '0')}/${startDate.month.toString().padLeft(2, '0')}/${startDate.year}';
    final endDateStr = '${endDate.day.toString().padLeft(2, '0')}/${endDate.month.toString().padLeft(2, '0')}/${endDate.year}';
    return '$name | $startDateStr – $endDateStr';
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'folderPath': folderPath,
    };
  }

  factory Chat.fromMap(Map<String, dynamic> map) {
    return Chat(
      id: map['id'],
      name: map['name'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      endDate: DateTime.fromMillisecondsSinceEpoch(map['endDate']),
      folderPath: map['folderPath'],
      messages: [], // سيتم تحميل الرسائل بشكل منفصل
    );
  }
}
