import 'package:flutter/material.dart';
import 'dart:io';
import '../models/chat_message.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'media_gallery_widget.dart';
import 'audio_player_widget.dart';
import 'video_thumbnail_widget.dart';
import 'pdf_viewer_widget.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;

class MessageBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isMe;
  final VoidCallback? onLongPress; // للضغط المطول لإضافة للمفضلة
  final bool isFavorite; // هل الرسالة في المفضلة
  final bool isHighlighted; // للتمييز البصري عند الانتقال من المفضلة
  final bool showDateHeader; // لعرض رأس التاريخ

  const MessageBubble({
    super.key,
    required this.message,
    this.isMe = false,
    this.onLongPress,
    this.isFavorite = false,
    this.isHighlighted = false,
    this.showDateHeader = false,
  });

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat('HH:mm');

    return GestureDetector(
      onLongPress: onLongPress,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        child: Column(
          crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            // عرض التاريخ إذا كان مختلفاً عن الرسالة السابقة
            _buildDateHeader(context),

            Row(
              mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
              children: [
                // للوسائط (صور وفيديوهات) نستخدم widget خاص
                if (message.type == 'image' || message.type == 'video')
                  Flexible(child: _buildMediaMessage(context))
                else
                  Flexible(
                    child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      // تمييز بصري للرسالة المستهدفة أو الألوان العادية
                      color: isHighlighted
                          ? const Color(0xFFFFEB3B).withValues(alpha: 0.4) // تمييز أصفر للرسالة المستهدفة
                          : (isMe ? const Color(0xFFDCF8C6) : const Color(0xFF262D31)),
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(18),
                        topRight: const Radius.circular(18),
                        bottomLeft: isMe ? const Radius.circular(4) : const Radius.circular(18),
                        bottomRight: isMe ? const Radius.circular(18) : const Radius.circular(4),
                      ),
                      border: isHighlighted
                          ? Border.all(color: const Color(0xFFFFEB3B), width: 2)
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: isHighlighted
                              ? const Color(0xFFFFEB3B).withValues(alpha: 0.5)
                              : Colors.black.withValues(alpha: 0.2),
                          spreadRadius: isHighlighted ? 2 : 1,
                          blurRadius: isHighlighted ? 8 : 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم المرسل (للرسائل المستلمة فقط)
                        if (!isMe) ...[
                          Text(
                            message.sender,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF25D366),
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                        ],

                        // محتوى الرسالة
                        _buildMessageContent(context),

                        const SizedBox(height: 4),

                        // الوقت
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // أيقونة النجمة للمفضلة
                            if (isFavorite) ...[
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Colors.amber,
                              ),
                              const SizedBox(width: 4),
                            ],
                            Text(
                              timeFormat.format(message.timestamp),
                              style: TextStyle(
                                fontSize: 12,
                                color: isMe
                                    ? Colors.black.withValues(alpha: 0.6) // أخضر فاتح - نص داكن شفاف
                                    : Colors.white.withValues(alpha: 0.6), // رمادي داكن - نص أبيض شفاف
                              ),
                            ),
                            if (isMe) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.done_all,
                                size: 16,
                                color: Colors.black.withValues(alpha: 0.6),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateHeader(BuildContext context) {
    if (!showDateHeader) return const SizedBox.shrink();

    final dateFormat = DateFormat('dd MMMM yyyy', 'ar');
    final dateString = dateFormat.format(message.timestamp);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF25D366).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF25D366).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            dateString,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF25D366),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    // للصور والفيديوهات، نحتاج لإزالة padding من الفقاعة
    if (message.type == 'image' || message.type == 'video') {
      return _buildMediaMessage(context);
    }

    switch (message.type) {
      case 'text':
        return Text(
          message.content,
          style: TextStyle(
            fontSize: 16,
            color: isMe ? Colors.black : Colors.white, // أخضر فاتح - نص أسود، رمادي داكن - نص أبيض
          ),
        );

      case 'audio':
        return _buildAudioMessage(context);

      case 'document':
        return _buildDocumentMessage(context);

      case 'link':
        return _buildLinkMessage(context);

      case 'call':
        return _buildCallMessage(context);

      case 'deleted':
        return _buildDeletedMessage(context);

      case 'unknown_call':
        return _buildUnknownCallMessage(context);

      default:
        return Row(
          children: [
            Icon(Icons.help_outline, color: isMe ? Colors.black.withValues(alpha: 0.7) : Colors.grey),
            const SizedBox(width: 8),
            Text(
              '❓ محتوى غير مدعوم',
              style: TextStyle(color: isMe ? Colors.black.withValues(alpha: 0.7) : Colors.grey),
            ),
          ],
        );
    }
  }

  // رسائل الوسائط (صور وفيديوهات) بدون padding
  Widget _buildMediaMessage(BuildContext context) {
    final timeFormat = DateFormat('HH:mm');
    Widget mediaWidget;

    if (message.type == 'image') {
      mediaWidget = _buildImageMessage(context);
    } else {
      mediaWidget = _buildVideoMessage(context);
    }

    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      decoration: BoxDecoration(
        // المرسل (أنا): أخضر فاتح واتساب، المستلم: رمادي داكن واتساب
        color: isMe ? const Color(0xFFDCF8C6) : const Color(0xFF262D31),
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(18),
          topRight: const Radius.circular(18),
          bottomLeft: isMe ? const Radius.circular(4) : const Radius.circular(18),
          bottomRight: isMe ? const Radius.circular(18) : const Radius.circular(4),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم المرسل (للرسائل المستلمة فقط)
          if (!isMe) ...[
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 6),
              child: Text(
                message.sender,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF25D366),
                  fontSize: 12,
                ),
              ),
            ),
          ],

          // محتوى الوسائط بدون padding
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: !isMe && message.sender.isNotEmpty ? const Radius.circular(0) : const Radius.circular(18),
              topRight: !isMe && message.sender.isNotEmpty ? const Radius.circular(0) : const Radius.circular(18),
              bottomLeft: isMe ? const Radius.circular(4) : const Radius.circular(18),
              bottomRight: isMe ? const Radius.circular(18) : const Radius.circular(4),
            ),
            child: mediaWidget,
          ),

          // الوقت
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12, bottom: 6, top: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // أيقونة النجمة للمفضلة
                if (isFavorite) ...[
                  Icon(
                    Icons.star,
                    size: 14,
                    color: Colors.amber,
                  ),
                  const SizedBox(width: 4),
                ],
                Text(
                  timeFormat.format(message.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: isMe
                        ? Colors.black.withValues(alpha: 0.6) // أخضر فاتح - نص داكن شفاف
                        : Colors.white.withValues(alpha: 0.6), // رمادي داكن - نص أبيض شفاف
                  ),
                ),
                if (isMe) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.done_all,
                    size: 16,
                    color: Colors.black.withValues(alpha: 0.6),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageMessage(BuildContext context) {
    debugPrint('🖼️ عرض رسالة صورة: ${message.filePath}');
    if (message.filePath != null && File(message.filePath!).existsSync()) {
      debugPrint('✅ ملف الصورة موجود: ${message.filePath}');
      return ThumbnailWidget(
        imagePath: message.filePath!,
        size: 200,
        onTap: () {
          debugPrint('🖼️ فتح معرض الصور...');
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => MediaGalleryWidget(
                mediaPaths: [message.filePath!],
                initialIndex: 0,
                mediaTypes: ['image'],
              ),
            ),
          );
        },
      );
    } else {
      debugPrint('❌ ملف الصورة غير موجود: ${message.filePath}');
      return Row(
        children: [
          Icon(Icons.image, color: isMe ? Colors.black.withValues(alpha: 0.7) : Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '📷 ${message.content}',
              style: TextStyle(color: isMe ? Colors.black : Colors.white),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildVideoMessage(BuildContext context) {
    debugPrint('🎥 عرض رسالة فيديو: ${message.filePath}');
    if (message.filePath != null && File(message.filePath!).existsSync()) {
      debugPrint('✅ ملف الفيديو موجود: ${message.filePath}');
      return VideoPreviewWidget(
        videoPath: message.filePath!,
        width: 250,
        height: 180,
        isMyMessage: isMe,
        onTap: () {
          debugPrint('🎥 فتح مشغل الفيديو...');
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => MediaGalleryWidget(
                mediaPaths: [message.filePath!],
                initialIndex: 0,
                mediaTypes: ['video'],
              ),
            ),
          );
        },
      );
    } else {
      debugPrint('❌ ملف الفيديو غير موجود: ${message.filePath}');
      return GestureDetector(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('❌ ملف الفيديو غير موجود')),
          );
        },
        child: Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.black12,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_fill,
                size: 40,
                color: isMe ? Colors.black.withValues(alpha: 0.7) : Colors.white,
              ),
              const SizedBox(height: 8),
              Text(
                '🎥 ${message.content}',
                style: TextStyle(
                  color: isMe ? Colors.black : Colors.white,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildAudioMessage(BuildContext context) {
    debugPrint('🎧 عرض رسالة صوتية: ${message.filePath}');
    if (message.filePath != null && File(message.filePath!).existsSync()) {
      debugPrint('✅ ملف الصوت موجود: ${message.filePath}');
      return AudioPlayerWidget(
        audioPath: message.filePath!,
        isMe: isMe,
      );
    } else {
      debugPrint('❌ ملف الصوت غير موجود: ${message.filePath}');
      return GestureDetector(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('❌ ملف الصوت غير موجود')),
          );
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.audiotrack,
              color: isMe ? Colors.black : const Color(0xFF25D366),
            ),
            const SizedBox(width: 8),
            Text(
              '🎧 ${message.content}',
              style: TextStyle(color: isMe ? Colors.black : Colors.white),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildDocumentMessage(BuildContext context) {
    // الحصول على اسم الملف الحقيقي
    String displayName;
    String fileExtension;

    if (message.filePath != null && File(message.filePath!).existsSync()) {
      // إذا كان الملف موجود، استخدم اسمه الحقيقي
      final realFileName = path.basename(message.filePath!);
      displayName = realFileName;
      fileExtension = path.extension(realFileName).toLowerCase().replaceFirst('.', '');
    } else {
      // إذا لم يكن الملف موجود، استخدم اسم من المحتوى
      final fileName = message.content.split('/').last.split('\\').last; // دعم مسارات Windows أيضاً
      displayName = fileName;
      fileExtension = fileName.split('.').last.toLowerCase();
    }

    // لا نحسن اسم العرض - نعرض الاسم الحقيقي دائماً
    // displayName = _getDisplayFileName(displayName, fileExtension);

    // إذا كان الاسم طويل جداً، اقطعه بذكاء
    if (displayName.length > 40) {
      final namePart = displayName.substring(0, displayName.lastIndexOf('.'));
      final extPart = displayName.substring(displayName.lastIndexOf('.'));
      if (namePart.length > 30) {
        displayName = '${namePart.substring(0, 30)}...$extPart';
      }
    }

    return GestureDetector(
      onTap: () async {
        debugPrint('🔍 محاولة فتح مستند: ${message.filePath}');
        if (message.filePath != null && File(message.filePath!).existsSync()) {
          try {
            // للملفات PDF، فتح عارض PDF داخلي
            if (fileExtension == 'pdf') {
              debugPrint('📄 فتح PDF في العارض الداخلي...');
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PdfViewerWidget(
                    pdfPath: message.filePath!,
                    fileName: displayName,
                  ),
                ),
              );
              return;
            }

            // للملفات الأخرى، فتح نافذة "فتح باستخدام"
            debugPrint('📄 فتح المستند باستخدام نافذة "فتح باستخدام"...');

            // معالجة خاصة لملفات APK
            if (fileExtension == 'apk') {
              debugPrint('📱 محاولة فتح ملف APK...');
              final result = await OpenFile.open(
                message.filePath!,
                type: 'application/vnd.android.package-archive',
              );
              debugPrint('📱 نتيجة فتح APK: ${result.message}');

              if (result.type != ResultType.done) {
                // إذا فشل، جرب بدون تحديد النوع
                final fallbackResult = await OpenFile.open(message.filePath!);
                if (fallbackResult.type != ResultType.done) {
                  throw Exception('لا يمكن فتح ملف APK: ${fallbackResult.message}');
                }
              }
            } else {
              // للملفات الأخرى
              final result = await OpenFile.open(message.filePath!);
              debugPrint('📄 نتيجة فتح المستند: ${result.message}');

              if (result.type != ResultType.done) {
                throw Exception(result.message);
              }
            }
          } catch (e) {
            debugPrint('❌ خطأ في فتح المستند: $e');
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ خطأ في فتح المستند: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else {
          debugPrint('❌ ملف المستند غير موجود: ${message.filePath}');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ ملف المستند غير موجود'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isMe
              ? Colors.black.withValues(alpha: 0.1)
              : Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getDocumentColor(fileExtension).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getDocumentColor(fileExtension).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getDocumentIcon(fileExtension),
                color: _getDocumentColor(fileExtension),
                size: 28,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    displayName,
                    style: TextStyle(
                      color: isMe ? Colors.black : Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.description,
                        size: 12,
                        color: isMe
                            ? Colors.black.withValues(alpha: 0.6)
                            : Colors.white.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getFileTypeLabel(fileExtension),
                        style: TextStyle(
                          color: isMe
                              ? Colors.black.withValues(alpha: 0.6)
                              : Colors.white.withValues(alpha: 0.6),
                          fontSize: 12,
                        ),
                      ),
                      const Spacer(),
                      if (message.filePath != null && File(message.filePath!).existsSync())
                        Icon(
                          Icons.check_circle,
                          size: 12,
                          color: Colors.green,
                        )
                      else
                        Icon(
                          Icons.error,
                          size: 12,
                          color: Colors.red,
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkMessage(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        debugPrint('🔗 محاولة فتح رابط: ${message.content}');
        try {
          String url = message.content;

          // التأكد من وجود بروتوكول
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://$url';
          }

          final uri = Uri.parse(url);
          debugPrint('🔗 URI parsed: $uri');

          // فتح الرابط مباشرة في المتصفح الخارجي
          debugPrint('🔗 فتح الرابط في المتصفح الخارجي...');
          await launchUrl(
            uri,
            mode: LaunchMode.externalApplication,
            webViewConfiguration: const WebViewConfiguration(
              enableJavaScript: true,
              enableDomStorage: true,
            ),
          );
          debugPrint('✅ تم فتح الرابط بنجاح');
        } catch (e) {
          debugPrint('❌ خطأ في فتح الرابط: $e');
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('❌ خطأ في فتح الرابط: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.link,
                  color: Colors.blue,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    message.content,
                    style: const TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '🔗 اضغط للفتح في المتصفح',
              style: TextStyle(
                color: Colors.blue.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCallMessage(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.phone,
          color: isMe ? Colors.black : const Color(0xFF25D366),
        ),
        const SizedBox(width: 8),
        Text(
          '📞 مكالمة صوتية',
          style: TextStyle(color: isMe ? Colors.black : Colors.white),
        ),
      ],
    );
  }

  Widget _buildUnknownCallMessage(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تفاصيل المكالمة'),
            content: const Text(
              'لا يمكن تحديد نوع المكالمة (صوتية/فيديو)،\nالتفاصيل غير متوفرة في النسخة الاحتياطية.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.phone,
            color: isMe ? Colors.black : const Color(0xFF25D366),
          ),
          const SizedBox(width: 8),
          Text(
            '📞 مكالمة — اضغط للتفاصيل',
            style: TextStyle(
              color: isMe ? Colors.black : Colors.white,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
    );
  }

  // دالة لتحديد أيقونة المستند حسب النوع
  IconData _getDocumentIcon(String extension) {
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'apk':
        return Icons.android;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mkv':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  // دالة لتحديد لون المستند حسب النوع
  Color _getDocumentColor(String extension) {
    switch (extension) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'xls':
      case 'xlsx':
        return Colors.green;
      case 'ppt':
      case 'pptx':
        return Colors.orange;
      case 'txt':
        return Colors.grey;
      case 'apk':
        return Colors.green;
      case 'zip':
      case 'rar':
      case '7z':
        return Colors.purple;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Colors.pink;
      case 'mp4':
      case 'avi':
      case 'mkv':
        return Colors.indigo;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Colors.teal;
      default:
        return isMe ? Colors.black : const Color(0xFF25D366);
    }
  }

  /// بناء رسالة محذوفة
  Widget _buildDeletedMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.block,
            size: 16,
            color: isMe ? Colors.black54 : Colors.white54,
          ),
          const SizedBox(width: 8),
          Text(
            'تم حذف هذه الرسالة',
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: isMe ? Colors.black54 : Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  // دالة لتحسين اسم الملف للعرض
  String _getDisplayFileName(String fileName, String extension) {
    // إذا كان الاسم طويل ومشفر، اعرض اسم مبسط
    if (fileName.length > 30 && fileName.contains(RegExp(r'[a-f0-9]{8,}'))) {
      // ملف مشفر، اعرض نوع الملف بدلاً من الاسم
      return _getFileTypeLabel(extension);
    }

    // إذا كان الاسم عادي، اعرضه كما هو
    return fileName;
  }

  // دالة لتحديد تسمية نوع الملف
  String _getFileTypeLabel(String extension) {
    switch (extension) {
      case 'pdf':
        return 'مستند PDF';
      case 'doc':
      case 'docx':
        return 'مستند Word';
      case 'xls':
      case 'xlsx':
        return 'جدول Excel';
      case 'ppt':
      case 'pptx':
        return 'عرض PowerPoint';
      case 'txt':
        return 'ملف نصي';
      case 'apk':
        return 'تطبيق Android';
      case 'zip':
        return 'ملف مضغوط ZIP';
      case 'rar':
        return 'ملف مضغوط RAR';
      case '7z':
        return 'ملف مضغوط 7Z';
      case 'jpg':
      case 'jpeg':
        return 'صورة JPEG';
      case 'png':
        return 'صورة PNG';
      case 'gif':
        return 'صورة GIF';
      case 'mp4':
        return 'فيديو MP4';
      case 'avi':
        return 'فيديو AVI';
      case 'mkv':
        return 'فيديو MKV';
      case 'mp3':
        return 'ملف صوتي MP3';
      case 'wav':
        return 'ملف صوتي WAV';
      case 'flac':
        return 'ملف صوتي FLAC';
      default:
        return 'مستند';
    }
  }
}
