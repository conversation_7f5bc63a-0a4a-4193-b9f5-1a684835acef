import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat.dart';
import '../models/chat_message.dart';
import '../services/database_service.dart';
import '../widgets/message_bubble.dart';
import '../widgets/media_tabs_widget.dart';
import '../screens/chat_statistics_screen.dart';
import '../screens/favorites_screen.dart';
import '../services/chat_share_service.dart';

class ChatScreen extends StatefulWidget {
  final Chat chat;

  const ChatScreen({super.key, required this.chat});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  List<ChatMessage> _messages = [];
  List<ChatMessage> _filteredMessages = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String? _currentUserName;
  bool _isCurrentUserSwapped = false; // حالة التبديل بين المرسل والمستقبل
  final ScrollController _scrollController = ScrollController(); // للتنقل للرسائل
  int _currentSearchIndex = 0; // فهرس نتيجة البحث الحالية
  List<int> _searchResultIndices = []; // فهارس نتائج البحث في القائمة الأصلية
  Set<String> _favoriteMessageIds = {}; // معرفات الرسائل المفضلة

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _loadSwapState();
    _loadFavorites();
  }

  /// تحميل حالة التبديل المحفوظة
  Future<void> _loadSwapState() async {
    final prefs = await SharedPreferences.getInstance();
    final swapKey = 'chat_swap_${widget.chat.id}';
    setState(() {
      _isCurrentUserSwapped = prefs.getBool(swapKey) ?? false;
    });
  }

  /// حفظ حالة التبديل
  Future<void> _saveSwapState() async {
    final prefs = await SharedPreferences.getInstance();
    final swapKey = 'chat_swap_${widget.chat.id}';
    await prefs.setBool(swapKey, _isCurrentUserSwapped);
  }

  /// تحميل المفضلة المحفوظة
  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chat.id}';
    final favoritesList = prefs.getStringList(favoritesKey) ?? [];
    setState(() {
      _favoriteMessageIds = favoritesList.toSet();
    });
  }

  /// حفظ المفضلة
  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chat.id}';
    await prefs.setStringList(favoritesKey, _favoriteMessageIds.toList());

    debugPrint('💾 تم حفظ المفضلة بالمفتاح: $favoritesKey');
    debugPrint('📋 المعرفات المحفوظة: ${_favoriteMessageIds.toList()}');
    debugPrint('🔢 عدد المفضلة: ${_favoriteMessageIds.length}');
    debugPrint('📱 معرف المحادثة: ${widget.chat.id}');
  }

  /// إنشاء معرف فريد للرسالة
  String _createMessageId(ChatMessage message) {
    // استخدام معرف ثابت ومتسق
    return '${message.timestamp.millisecondsSinceEpoch}_${message.sender.hashCode}_${message.content.hashCode}';
  }

  /// تبديل حالة المفضلة للرسالة
  void _toggleFavorite(ChatMessage message) {
    final messageId = _createMessageId(message);

    debugPrint('🌟 تبديل المفضلة للرسالة:');
    debugPrint('   المعرف: $messageId');
    debugPrint('   المرسل: ${message.sender}');
    debugPrint('   المحتوى: ${message.content.length > 50 ? message.content.substring(0, 50) + '...' : message.content}');

    setState(() {
      if (_favoriteMessageIds.contains(messageId)) {
        _favoriteMessageIds.remove(messageId);
        debugPrint('   ❌ تم إزالة من المفضلة');
      } else {
        _favoriteMessageIds.add(messageId);
        debugPrint('   ⭐ تم إضافة للمفضلة');
      }
    });

    _saveFavorites();

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _favoriteMessageIds.contains(messageId)
            ? '⭐ تم إضافة الرسالة للمفضلة'
            : '🗑️ تم إزالة الرسالة من المفضلة',
        ),
        backgroundColor: const Color(0xFF25D366),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// التحقق من كون الرسالة في المفضلة
  bool _isFavorite(ChatMessage message) {
    final messageId = _createMessageId(message);
    return _favoriteMessageIds.contains(messageId);
  }

  /// التمرير للرسالة المحددة (للانتقال من المفضلة)
  void _scrollToMessage(ChatMessage targetMessage) {
    // البحث عن فهرس الرسالة في القائمة المفلترة
    final messageIndex = _filteredMessages.indexWhere((message) =>
      message.timestamp == targetMessage.timestamp &&
      message.sender == targetMessage.sender &&
      message.content == targetMessage.content
    );

    if (messageIndex != -1 && _scrollController.hasClients) {
      // حساب الموقع (الرسائل معكوسة في ListView)
      final targetIndex = _filteredMessages.length - 1 - messageIndex;
      final itemHeight = 100.0; // تقدير ارتفاع الرسالة
      final targetOffset = targetIndex * itemHeight;

      // التمرير للرسالة مع تأثير بصري
      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOut,
      );

      // إظهار رسالة تأكيد
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('📍 تم الانتقال للرسالة المفضلة'),
          backgroundColor: const Color(0xFF25D366),
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // الرسالة غير موجودة في النتائج الحالية
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('⚠️ الرسالة غير موجودة في النتائج الحالية'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _loadMessages() async {
    try {
      final messages = await DatabaseService.getChatMessages(widget.chat.id);
      if (mounted) {
        // تحديد المستخدم الحالي (الأكثر إرسالاً للرسائل)
        final senderCounts = <String, int>{};
        for (final message in messages) {
          senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
        }

        if (senderCounts.isNotEmpty) {
          _currentUserName = senderCounts.entries
              .reduce((a, b) => a.value > b.value ? a : b)
              .key;
        }

        setState(() {
          _messages = messages;
          _filteredMessages = messages;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الرسائل: $e')),
        );
      }
    }
  }

  void _searchMessages(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredMessages = _messages;
        _isSearching = false;
        _searchResultIndices.clear();
        _currentSearchIndex = 0;
      });
    } else {
      // البحث في الرسائل وحفظ الفهارس
      _searchResultIndices.clear();
      final filteredMessages = <ChatMessage>[];

      for (int i = 0; i < _messages.length; i++) {
        if (_messages[i].content.toLowerCase().contains(query.toLowerCase())) {
          _searchResultIndices.add(i);

          // إضافة السياق (رسالة قبل ورسالة بعد)
          final contextStart = (i - 1).clamp(0, _messages.length - 1);
          final contextEnd = (i + 1).clamp(0, _messages.length - 1);

          for (int j = contextStart; j <= contextEnd; j++) {
            if (!filteredMessages.contains(_messages[j])) {
              filteredMessages.add(_messages[j]);
            }
          }
        }
      }

      // ترتيب الرسائل حسب الوقت
      filteredMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      setState(() {
        _filteredMessages = filteredMessages;
        _isSearching = true;
        _currentSearchIndex = 0;
      });
    }
  }

  /// الانتقال لنتيجة البحث التالية
  void _goToNextSearchResult() {
    if (_searchResultIndices.isNotEmpty) {
      setState(() {
        _currentSearchIndex = (_currentSearchIndex + 1) % _searchResultIndices.length;
      });
      _scrollToSearchResult();
    }
  }

  /// الانتقال لنتيجة البحث السابقة
  void _goToPreviousSearchResult() {
    if (_searchResultIndices.isNotEmpty) {
      setState(() {
        _currentSearchIndex = (_currentSearchIndex - 1 + _searchResultIndices.length) % _searchResultIndices.length;
      });
      _scrollToSearchResult();
    }
  }

  /// التمرير لنتيجة البحث الحالية
  void _scrollToSearchResult() {
    if (_searchResultIndices.isNotEmpty && _scrollController.hasClients) {
      final targetIndex = _searchResultIndices[_currentSearchIndex];
      final itemHeight = 100.0; // تقدير ارتفاع الرسالة
      final targetOffset = targetIndex * itemHeight;

      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  /// عرض المفضلة
  void _showFavorites() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FavoritesScreen(
          chatId: widget.chat.id,
          chatName: widget.chat.name,
          messages: _messages,
          onMessageTap: _scrollToMessage,
        ),
      ),
    );
  }

  /// عرض الوسائط
  void _showMedia() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MediaTabsWidget(
          messages: _messages,
          chatName: widget.chat.name,
          chatId: widget.chat.id,
          onMessageTap: _scrollToMessage,
        ),
      ),
    );
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatStatisticsScreen(
          messages: _messages,
          chatName: widget.chat.name,
        ),
      ),
    );
  }

  /// تبديل المحادثة
  void _toggleSwap() {
    setState(() {
      _isCurrentUserSwapped = !_isCurrentUserSwapped;
    });
    _saveSwapState();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isCurrentUserSwapped
            ? '🔄 تم تبديل المرسل والمستقبل'
            : '↩️ تم إرجاع الترتيب الأصلي',
        ),
        backgroundColor: const Color(0xFF25D366),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// تبديل البحث المتقدم
  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _filteredMessages = _messages;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: const Color(0xFF0C1317), // خلفية داكنة مثل واتساب
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.chat.name,
                style: const TextStyle(fontSize: 18),
              ),
              if (_isCurrentUserSwapped)
                const Text(
                  '🔄 تم تبديل المرسل والمستقبل',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF25D366),
                    fontWeight: FontWeight.normal,
                  ),
                ),
            ],
          ),
          backgroundColor: const Color(0xFF2A2F32),
          foregroundColor: Colors.white,
          actions: [
            // زر التبديل السريع (صغير)
            IconButton(
              icon: Icon(
                _isCurrentUserSwapped ? Icons.swap_horiz : Icons.swap_horizontal_circle,
                color: _isCurrentUserSwapped ? const Color(0xFF25D366) : Colors.white,
                size: 20,
              ),
              tooltip: 'تبديل سريع',
              onPressed: () {
                setState(() {
                  _isCurrentUserSwapped = !_isCurrentUserSwapped;
                });
                _saveSwapState();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      _isCurrentUserSwapped
                        ? '🔄 تم التبديل'
                        : '↩️ تم الإرجاع',
                    ),
                    backgroundColor: const Color(0xFF25D366),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
            ),

            // قائمة المزيد
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                switch (value) {
                  case 'favorites':
                    _showFavorites();
                    break;
                  case 'media':
                    _showMedia();
                    break;
                  case 'statistics':
                    _showStatistics();
                    break;
                  case 'swap':
                    _toggleSwap();
                    break;
                  case 'search':
                    _toggleSearch();
                    break;
                  case 'share_text':
                    ChatShareService.shareMessages(_messages, widget.chat.name, context);
                    break;
                  case 'share_file':
                    ChatShareService.shareAsTextFile(_messages, widget.chat.name, context);
                    break;
                  case 'share_stats':
                    ChatShareService.shareChatStatistics(_messages, widget.chat.name, context);
                    break;
                  case 'share_options':
                    ChatShareService.showShareOptions(_messages, widget.chat.name, context);
                    break;
                  case 'reset_swap':
                    setState(() {
                      _isCurrentUserSwapped = false;
                    });
                    _saveSwapState();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('↩️ تم إرجاع الترتيب الأصلي للرسائل'),
                        backgroundColor: Color(0xFF25D366),
                        duration: Duration(seconds: 2),
                      ),
                    );
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'favorites',
                  child: Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber),
                      SizedBox(width: 8),
                      Text('⭐ المفضلة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'media',
                  child: Row(
                    children: [
                      Icon(Icons.attach_file, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('📎 الوسائط'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'statistics',
                  child: Row(
                    children: [
                      Icon(Icons.bar_chart, color: Colors.green),
                      SizedBox(width: 8),
                      Text('📊 الإحصائيات'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'swap',
                  child: Row(
                    children: [
                      Icon(Icons.swap_horiz, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('🔄 تبديل المحادثة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'search',
                  child: Row(
                    children: [
                      Icon(Icons.search, color: Colors.purple),
                      SizedBox(width: 8),
                      Text('🔍 البحث المتقدم'),
                    ],
                  ),
                ),
                // فاصل
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'share_text',
                  child: Row(
                    children: [
                      Icon(Icons.text_fields, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('📝 مشاركة كنص'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_file',
                  child: Row(
                    children: [
                      Icon(Icons.description, color: Colors.green),
                      SizedBox(width: 8),
                      Text('📄 مشاركة كملف'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_options',
                  child: Row(
                    children: [
                      Icon(Icons.share, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('📤 خيارات المشاركة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_stats',
                  child: Row(
                    children: [
                      Icon(Icons.analytics, color: Colors.purple),
                      SizedBox(width: 8),
                      Text('📊 مشاركة الإحصائيات'),
                    ],
                  ),
                ),
                if (_isCurrentUserSwapped)
                  const PopupMenuItem(
                    value: 'reset_swap',
                    child: Row(
                      children: [
                        Icon(Icons.refresh, color: Colors.red),
                        SizedBox(width: 8),
                        Text('↩️ إرجاع الترتيب الأصلي'),
                      ],
                    ),
                  ),
              ],
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(60),
            child: Container(
              padding: const EdgeInsets.all(8),
              color: const Color(0xFF2A2F32),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'البحث في المحادثة...',
                  hintStyle: const TextStyle(color: Colors.white54),
                  prefixIcon: const Icon(Icons.search, color: Colors.white54),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // عداد نتائج البحث
                            if (_isSearching && _searchResultIndices.isNotEmpty)
                              Text(
                                '${_currentSearchIndex + 1}/${_searchResultIndices.length}',
                                style: const TextStyle(color: Colors.white70, fontSize: 12),
                              ),

                            // زر السابق
                            if (_isSearching && _searchResultIndices.length > 1)
                              IconButton(
                                icon: const Icon(Icons.keyboard_arrow_up, color: Colors.white54),
                                onPressed: _goToPreviousSearchResult,
                                iconSize: 20,
                              ),

                            // زر التالي
                            if (_isSearching && _searchResultIndices.length > 1)
                              IconButton(
                                icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white54),
                                onPressed: _goToNextSearchResult,
                                iconSize: 20,
                              ),

                            // زر المسح
                            IconButton(
                              icon: const Icon(Icons.clear, color: Colors.white54),
                              onPressed: () {
                                _searchController.clear();
                                _searchMessages('');
                              },
                            ),
                          ],
                        )
                      : null,
                  filled: true,
                  fillColor: const Color(0xFF3C4043),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                ),
                onChanged: _searchMessages,
              ),
            ),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _filteredMessages.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isSearching ? Icons.search_off : Icons.message,
                          size: 100,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          _isSearching ? 'لا توجد نتائج للبحث' : 'لا توجد رسائل',
                          style: const TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    reverse: true, // لعرض الرسائل الأحدث في الأسفل
                    itemCount: _filteredMessages.length,
                    itemBuilder: (context, index) {
                      final message = _filteredMessages[_filteredMessages.length - 1 - index];
                      // تحديد المرسل مع مراعاة حالة التبديل
                      bool isMe = _currentUserName != null && message.sender == _currentUserName;
                      if (_isCurrentUserSwapped) {
                        isMe = !isMe; // عكس الحالة عند التبديل
                      }
                      return MessageBubble(
                        message: message,
                        isMe: isMe,
                        isFavorite: _isFavorite(message),
                        onLongPress: () => _toggleFavorite(message),
                      );
                    },
                  ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
