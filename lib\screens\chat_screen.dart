import 'package:flutter/material.dart';
import '../models/chat.dart';
import '../models/chat_message.dart';
import '../services/database_service.dart';
import '../widgets/message_bubble.dart';
import '../widgets/media_tabs_widget.dart';
import '../screens/chat_statistics_screen.dart';
import '../services/chat_share_service.dart';

class ChatScreen extends StatefulWidget {
  final Chat chat;

  const ChatScreen({super.key, required this.chat});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  List<ChatMessage> _messages = [];
  List<ChatMessage> _filteredMessages = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String? _currentUserName;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  Future<void> _loadMessages() async {
    try {
      final messages = await DatabaseService.getChatMessages(widget.chat.id);
      if (mounted) {
        // تحديد المستخدم الحالي (الأكثر إرسالاً للرسائل)
        final senderCounts = <String, int>{};
        for (final message in messages) {
          senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
        }

        if (senderCounts.isNotEmpty) {
          _currentUserName = senderCounts.entries
              .reduce((a, b) => a.value > b.value ? a : b)
              .key;
        }

        setState(() {
          _messages = messages;
          _filteredMessages = messages;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الرسائل: $e')),
        );
      }
    }
  }

  void _searchMessages(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredMessages = _messages;
        _isSearching = false;
      });
    } else {
      setState(() {
        _filteredMessages = _messages
            .where((message) => message.content.toLowerCase().contains(query.toLowerCase()))
            .toList();
        _isSearching = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: const Color(0xFF0C1317), // خلفية داكنة مثل واتساب
        appBar: AppBar(
          title: Text(widget.chat.name),
          backgroundColor: const Color(0xFF2A2F32),
          foregroundColor: Colors.white,
          actions: [
            // زر الوسائط
            IconButton(
              icon: const Icon(Icons.attach_file),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => MediaTabsWidget(
                      messages: _messages,
                      chatName: widget.chat.name,
                    ),
                  ),
                );
              },
            ),

            // زر الإحصائيات
            IconButton(
              icon: const Icon(Icons.bar_chart),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ChatStatisticsScreen(
                      messages: _messages,
                      chatName: widget.chat.name,
                    ),
                  ),
                );
              },
            ),

            // قائمة المزيد
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                switch (value) {
                  case 'share_text':
                    ChatShareService.shareMessages(_messages, widget.chat.name, context);
                    break;
                  case 'share_file':
                    ChatShareService.shareAsTextFile(_messages, widget.chat.name, context);
                    break;
                  case 'share_stats':
                    ChatShareService.shareChatStatistics(_messages, widget.chat.name, context);
                    break;
                  case 'share_options':
                    ChatShareService.showShareOptions(_messages, widget.chat.name, context);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'share_text',
                  child: Row(
                    children: [
                      Icon(Icons.text_fields, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('📝 مشاركة كنص'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_file',
                  child: Row(
                    children: [
                      Icon(Icons.description, color: Colors.green),
                      SizedBox(width: 8),
                      Text('📄 مشاركة كملف'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_options',
                  child: Row(
                    children: [
                      Icon(Icons.share, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('📤 خيارات المشاركة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_stats',
                  child: Row(
                    children: [
                      Icon(Icons.analytics, color: Colors.purple),
                      SizedBox(width: 8),
                      Text('📊 مشاركة الإحصائيات'),
                    ],
                  ),
                ),
              ],
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(60),
            child: Container(
              padding: const EdgeInsets.all(8),
              color: const Color(0xFF2A2F32),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'البحث في المحادثة...',
                  hintStyle: const TextStyle(color: Colors.white54),
                  prefixIcon: const Icon(Icons.search, color: Colors.white54),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.white54),
                          onPressed: () {
                            _searchController.clear();
                            _searchMessages('');
                          },
                        )
                      : null,
                  filled: true,
                  fillColor: const Color(0xFF3C4043),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                ),
                onChanged: _searchMessages,
              ),
            ),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _filteredMessages.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isSearching ? Icons.search_off : Icons.message,
                          size: 100,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          _isSearching ? 'لا توجد نتائج للبحث' : 'لا توجد رسائل',
                          style: const TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    reverse: true, // لعرض الرسائل الأحدث في الأسفل
                    itemCount: _filteredMessages.length,
                    itemBuilder: (context, index) {
                      final message = _filteredMessages[_filteredMessages.length - 1 - index];
                      final isMe = _currentUserName != null && message.sender == _currentUserName;
                      return MessageBubble(
                        message: message,
                        isMe: isMe,
                      );
                    },
                  ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
