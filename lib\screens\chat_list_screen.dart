import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import '../models/chat.dart';
import '../services/database_service.dart';
import 'detailed_statistics_screen.dart';
import '../widgets/safe_area_wrapper.dart';
import '../services/whatsapp_importer.dart';
import '../services/biometric_service.dart';
import 'chat_screen.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  List<Chat> _chats = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  Future<void> _loadChats() async {
    try {
      final chats = await DatabaseService.getAllChats();
      if (mounted) {
        setState(() {
          // ترتيب المحادثات: الأحدث أولاً (حسب تاريخ الإنشاء)
          _chats = chats..sort((a, b) => b.id.compareTo(a.id));
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // لا نعرض رسالة خطأ في تحميل المحادثات - سيظهر فقط شاشة فارغة
        debugPrint('❌ خطأ في تحميل المحادثات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
        backgroundColor: const Color(0xFF0C1317),
        extraBottomPadding: 24.0, // مساحة إضافية للأسفل
        appBar: AppBar(
          title: const Text('عارض واتساب'),
          backgroundColor: const Color(0xFF2A2F32),
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () async {
                final result = await Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const SettingsScreen()),
                );
                if (result == true) {
                  _loadChats(); // إعادة تحميل المحادثات بعد الاستيراد
                }
              },
            ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF25D366)),
                ),
              )
            : _chats.isEmpty
                ? Container(
                    color: const Color(0xFF0C1317),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2A2F32),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.chat_bubble_outline,
                              size: 80,
                              color: Color(0xFF25D366),
                            ),
                          ),
                          const SizedBox(height: 24),
                          const Text(
                            'لا توجد محادثات مستوردة',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'اذهب للإعدادات لاستيراد محادثة جديدة',
                            style: TextStyle(fontSize: 16, color: Colors.white70),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          ElevatedButton.icon(
                            onPressed: () async {
                              final result = await Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const SettingsScreen()),
                              );
                              if (result == true) {
                                _loadChats();
                              }
                            },
                            icon: const Icon(Icons.upload_file, color: Colors.white),
                            label: const Text(
                              'استيراد محادثة',
                              style: TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF25D366),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : Container(
                    color: const Color(0xFF0C1317), // خلفية داكنة مثل واتساب
                    child: ListView.builder(
                      itemCount: _chats.length,
                      itemBuilder: (context, index) {
                        final chat = _chats[index];
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color(0xFF2A2F32),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                spreadRadius: 1,
                                blurRadius: 3,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            leading: CircleAvatar(
                              backgroundColor: const Color(0xFF25D366),
                              radius: 28,
                              child: Text(
                                chat.name.isNotEmpty ? chat.name[0].toUpperCase() : '💬',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(
                              chat.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            subtitle: Text(
                              '${DateFormat('dd/MM/yyyy').format(chat.startDate)} - ${DateFormat('dd/MM/yyyy').format(chat.endDate)}',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.white54,
                              size: 16,
                            ),
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => ChatScreen(chat: chat),
                                ),
                              ).then((_) {
                                // تحديث القائمة عند العودة
                                _loadChats();
                              });
                            },
                            onLongPress: () {
                              _showChatOptions(context, chat);
                            },
                          ),
                        );
                      },
                    ),
                  ),
    );
  }

  /// عرض خيارات المحادثة (حذف، إعادة تسمية، إلخ)
  void _showChatOptions(BuildContext context, Chat chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2F32),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Column(
          children: [
            Text(
              chat.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '${chat.messages.length} رسالة',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit, color: Colors.blue),
              title: const Text('إعادة تسمية', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.of(context).pop();
                _renameChatDialog(context, chat);
              },
            ),

            // تم حذف "إحصائيات تفصيلية" و "معلومات المحادثة" حسب طلب المستخدم

            ListTile(
              leading: const Icon(Icons.keyboard_arrow_up, color: Colors.orange),
              title: const Text('نقل للأعلى', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.of(context).pop();
                _moveChatUp(context, chat);
              },
            ),

            ListTile(
              leading: const Icon(Icons.keyboard_arrow_down, color: Colors.orange),
              title: const Text('نقل للأسفل', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.of(context).pop();
                _moveChatDown(context, chat);
              },
            ),

            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف المحادثة', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.of(context).pop();
                _deleteChatDialog(context, chat);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق', style: TextStyle(color: Colors.white70)),
          ),
        ],
      ),
    );
  }

  /// حذف المحادثة مع تأكيد
  void _deleteChatDialog(BuildContext context, Chat chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل أنت متأكد من حذف المحادثة؟'),
            const SizedBox(height: 8),
            Text(
              'اسم المحادثة: ${chat.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('عدد الرسائل: ${chat.messages.length}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تحذير: لا يمكن التراجع عن هذا الإجراء!',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteChat(context, chat);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ حذف المحادثة
  Future<void> _deleteChat(BuildContext context, Chat chat) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري حذف المحادثة...'),
            ],
          ),
        ),
      );

      // حذف من قاعدة البيانات
      await DatabaseService.deleteChat(chat.id);

      // حذف مجلد الملفات إذا كان موجوداً
      if (chat.folderPath != null) {
        final folder = Directory(chat.folderPath);
        if (await folder.exists()) {
          await folder.delete(recursive: true);
        }
      }

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();

        // تحديث القائمة
        _loadChats();

        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف المحادثة "${chat.name}" بنجاح'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'تراجع',
              onPressed: () {
                // يمكن إضافة إمكانية التراجع لاحقاً
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('عذراً، لا يمكن التراجع عن الحذف حالياً'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المحادثة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إعادة تسمية المحادثة
  void _renameChatDialog(BuildContext context, Chat chat) {
    final controller = TextEditingController(text: chat.name);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تسمية المحادثة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'اسم المحادثة الجديد',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newName = controller.text.trim();
              if (newName.isNotEmpty && newName != chat.name) {
                Navigator.of(context).pop();
                await _renameChat(context, chat, newName);
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// تنفيذ إعادة تسمية المحادثة
  Future<void> _renameChat(BuildContext context, Chat chat, String newName) async {
    try {
      // تحديث في قاعدة البيانات
      await DatabaseService.updateChatName(chat.id, newName);

      // تحديث القائمة
      _loadChats();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير اسم المحادثة إلى "$newName"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة التسمية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض معلومات المحادثة
  void _showChatInfo(BuildContext context, Chat chat) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Text('معلومات المحادثة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('الاسم:', chat.name),
            _buildInfoRow('عدد الرسائل:', '${chat.messages.length}'),
            _buildInfoRow('تاريخ البداية:', dateFormat.format(chat.startDate)),
            _buildInfoRow('تاريخ النهاية:', dateFormat.format(chat.endDate)),
            _buildInfoRow('مجلد الملفات:', chat.folderPath),

            const SizedBox(height: 16),
            const Text(
              'إحصائيات سريعة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // حساب إحصائيات سريعة
            ...() {
              final senderCounts = <String, int>{};
              final typeCounts = <String, int>{};

              for (final message in chat.messages) {
                senderCounts[message.sender] = (senderCounts[message.sender] ?? 0) + 1;
                typeCounts[message.type] = (typeCounts[message.type] ?? 0) + 1;
              }

              final topSender = senderCounts.entries
                  .reduce((a, b) => a.value > b.value ? a : b);

              return [
                _buildInfoRow('أكثر مرسل:', '${topSender.key} (${topSender.value} رسالة)'),
                _buildInfoRow('الرسائل النصية:', '${typeCounts['text'] ?? 0}'),
                _buildInfoRow('الصور:', '${typeCounts['image'] ?? 0}'),
                _buildInfoRow('الفيديوهات:', '${typeCounts['video'] ?? 0}'),
                _buildInfoRow('الصوتيات:', '${typeCounts['audio'] ?? 0}'),
              ];
            }(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// عرض إحصائيات تفصيلية احترافية
  void _showDetailedStatistics(BuildContext context, Chat chat) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DetailedStatisticsScreen(
          chat: chat,
        ),
      ),
    );
  }

  /// نقل المحادثة للأعلى
  void _moveChatUp(BuildContext context, Chat chat) {
    final currentIndex = _chats.indexWhere((c) => c.id == chat.id);
    if (currentIndex > 0) {
      setState(() {
        final temp = _chats[currentIndex];
        _chats[currentIndex] = _chats[currentIndex - 1];
        _chats[currentIndex - 1] = temp;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم نقل "${chat.name}" للأعلى'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('المحادثة في أعلى القائمة بالفعل'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// نقل المحادثة للأسفل
  void _moveChatDown(BuildContext context, Chat chat) {
    final currentIndex = _chats.indexWhere((c) => c.id == chat.id);
    if (currentIndex < _chats.length - 1) {
      setState(() {
        final temp = _chats[currentIndex];
        _chats[currentIndex] = _chats[currentIndex + 1];
        _chats[currentIndex + 1] = temp;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم نقل "${chat.name}" للأسفل'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('المحادثة في أسفل القائمة بالفعل'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}

// نقل SettingsScreen هنا مؤقتاً
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFF0C1317),
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: const Color(0xFF2A2F32),
          foregroundColor: Colors.white,
        ),
        body: Container(
          color: const Color(0xFF0C1317),
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2F32),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.upload_file, color: Colors.white),
                  ),
                  title: const Text(
                    'استيراد محادثة جديدة (ملف ZIP)',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'اختر ملف ZIP مُصدّر من واتساب',
                    style: TextStyle(color: Colors.white70),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.white54),
                  onTap: () => _importWhatsAppZip(context),
                ),
              ),

              // زر استيراد مجلد (ميزة جديدة!)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2F32),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF128C7E),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.folder_open, color: Colors.white),
                  ),
                  title: const Text(
                    'استيراد مجلد محادثة (أسرع!) 🚀',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'اختر مجلد مفكوك من ZIP - أداء أفضل للفيديوهات',
                    style: TextStyle(color: Colors.white70),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.white54),
                  onTap: () => _importWhatsAppFolder(context),
                ),
              ),

              // زر إعدادات البصمة
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2F32),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.fingerprint, color: Colors.white),
                  ),
                  title: const Text(
                    'الحماية بالبصمة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'تفعيل الحماية بالبصمة أو Face ID',
                    style: TextStyle(color: Colors.white70),
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // زر بديل للبصمة
                      ElevatedButton(
                        onPressed: () async {
                          debugPrint('🔒 تم الضغط على الزر البديل للبصمة');
                          try {
                            await BiometricService.showBiometricSettings(context);
                            debugPrint('✅ تم فتح إعدادات البصمة بنجاح');
                          } catch (e) {
                            debugPrint('❌ خطأ في فتح إعدادات البصمة: $e');
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('خطأ في فتح إعدادات البصمة: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF25D366),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        child: const Text('إعدادات', style: TextStyle(fontSize: 12)),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward_ios, color: Colors.white54),
                    ],
                  ),
                  onTap: () async {
                    debugPrint('🔒 تم الضغط على زر إعدادات البصمة');
                    try {
                      await BiometricService.showBiometricSettings(context);
                      debugPrint('✅ تم فتح إعدادات البصمة بنجاح');
                    } catch (e) {
                      debugPrint('❌ خطأ في فتح إعدادات البصمة: $e');
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('خطأ في فتح إعدادات البصمة: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                ),
              ),

              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2F32),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.info, color: Colors.white),
                  ),
                  title: const Text(
                    'حول التطبيق',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'معلومات عن التطبيق والمطور',
                    style: TextStyle(color: Colors.white70),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.white54),
                  onTap: () => _showAboutDialog(context),
                ),
              ),
            ],
          ),
        ),
    );
  }



  /// استيراد مجلد محادثة واتساب (أسرع من ZIP)
  void _importWhatsAppFolder(BuildContext context) async {
    try {
      // عرض نافذة تفسيرية أولاً
      final shouldContinue = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF1F2C34),
          title: const Text(
            '📁 استيراد مجلد محادثة',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            '🚀 هذه الطريقة أسرع وأفضل!\n\n'
            '✅ أداء أفضل للفيديوهات والصور\n'
            '✅ لا يحتاج فك ضغط\n'
            '✅ استهلاك أقل للذاكرة\n\n'
            '📋 كيفية الاستخدام:\n'
            '1. فك ضغط ملف ZIP يدوياً\n'
            '2. اختر المجلد المفكوك\n'
            '3. يجب أن يحتوي على ملف .txt و مجلد الوسائط\n\n'
            'هل تريد المتابعة؟',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء', style: TextStyle(color: Colors.white54)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: const Color(0xFF128C7E)),
              child: const Text('متابعة', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );

      if (shouldContinue != true) return;

      // اختيار المجلد
      final result = await FilePicker.platform.getDirectoryPath();

      if (result == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم اختيار مجلد'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // عرض مؤشر التحميل
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            backgroundColor: Color(0xFF1F2C34),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Color(0xFF128C7E)),
                SizedBox(height: 20),
                Text(
                  'جاري استيراد المجلد...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        );
      }

      try {
        // استيراد المحادثة من المجلد
        final chat = await WhatsAppImporter.importWhatsAppFolder(result);
        debugPrint('✅ تم تحليل المحادثة: ${chat.name} (${chat.messages.length} رسالة)');

        // حفظ في قاعدة البيانات
        await DatabaseService.insertChat(chat);
        await DatabaseService.insertMessages(chat.id, chat.messages);

        // إغلاق مؤشر التحميل
        if (context.mounted) {
          Navigator.of(context).pop();
        }

        // العودة للصفحة الرئيسية مع إشارة النجاح
        if (context.mounted) {
          Navigator.of(context).pop(true);
        }

        // عرض رسالة نجاح
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ تم استيراد المحادثة بنجاح: ${chat.name}'),
              backgroundColor: const Color(0xFF128C7E),
              duration: const Duration(seconds: 3),
            ),
          );
        }

      } catch (e) {
        // إغلاق مؤشر التحميل
        if (context.mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة خطأ مفصلة
        final errorMsg = '''
❌ خطأ في استيراد المجلد:
$e

💡 تأكد من:
• المجلد يحتوي على ملف .txt للمحادثة
• المجلد يحتوي على مجلد فرعي للوسائط
• المجلد مفكوك من ZIP صحيح لواتساب

📱 للمساعدة: جرب استيراد ZIP بدلاً من المجلد
        ''';

        if (context.mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1F2C34),
            title: const Text('خطأ في الاستيراد', style: TextStyle(color: Colors.red)),
            content: SingleChildScrollView(
              child: Text(errorMsg, style: const TextStyle(color: Colors.white70)),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        );
        }
      }

    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ عام: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _importWhatsAppZip(BuildContext context) async {
    try {
      // استيراد ZIP - يعمل على الهاتف والويب
      if (kIsWeb) {
        // دعم الويب - استيراد ZIP مع تحذير
        final shouldContinue = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('🌐 استيراد على الويب'),
            content: const Text(
              'استيراد ZIP على الويب محدود الوظائف:\n\n'
              '✅ يمكن قراءة النصوص والرسائل\n'
              '❌ لا يمكن عرض الصور والفيديوهات\n'
              '❌ لا يمكن تشغيل الملفات الصوتية\n\n'
              'للحصول على التجربة الكاملة، استخدم التطبيق على الهاتف.\n\n'
              'هل تريد المتابعة؟'
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('متابعة'),
              ),
            ],
          ),
        );

        if (shouldContinue != true) return;
      }

      // اختيار ملفات ZIP
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['zip'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        if (!context.mounted) return;

        // عرض مؤشر التحميل
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 20),
                Text('جاري استيراد ${result.files.length} محادثة...'),
              ],
            ),
          ),
        );

        int successCount = 0;
        int errorCount = 0;

        for (final file in result.files) {
          try {
            debugPrint('🔄 بدء استيراد الملف: ${file.name}');

            String? filePath;
            if (kIsWeb) {
              // على الويب: استخدام البايتات مباشرة
              if (file.bytes != null) {
                // إنشاء مسار وهمي للويب
                filePath = '/web_temp/${file.name}';
                debugPrint('🌐 استخدام البايتات مباشرة للويب: $filePath');

                // تمرير البايتات مباشرة للمستورد
                final chat = await WhatsAppImporter.importWhatsAppZipFromBytes(file.bytes!, file.name);
                debugPrint('✅ تم تحليل المحادثة: ${chat.name} (${chat.messages.length} رسالة)');

                // حفظ في قاعدة البيانات
                await DatabaseService.insertChat(chat);
                await DatabaseService.insertMessages(chat.id, chat.messages);
                debugPrint('💾 تم حفظ المحادثة في قاعدة البيانات');

                successCount++;
                continue; // تخطي باقي المعالجة
              } else {
                throw Exception('لا يمكن الوصول لبايتات الملف ${file.name}');
              }
            } else {
              // على الهاتف: استخدام المسار مباشرة
              filePath = file.path;
              debugPrint('📱 مسار الملف: $filePath');
            }

            if (filePath != null) {
              // استيراد المحادثة
              final chat = await WhatsAppImporter.importWhatsAppZip(filePath);
              debugPrint('✅ تم تحليل المحادثة: ${chat.name} (${chat.messages.length} رسالة)');

              // حفظ في قاعدة البيانات
              await DatabaseService.insertChat(chat);
              await DatabaseService.insertMessages(chat.id, chat.messages);
              debugPrint('💾 تم حفظ المحادثة في قاعدة البيانات');

              successCount++;
            } else {
              throw Exception('لا يمكن الوصول لملف ${file.name}');
            }
          } catch (e, stackTrace) {
            errorCount++;
            final errorMsg = 'خطأ في استيراد ${file.name}: $e';
            debugPrint('❌ $errorMsg');
            debugPrint('📍 Stack trace: $stackTrace');

            // عرض رسالة خطأ مفصلة للمستخدم
            if (!context.mounted) return;

            // إغلاق مؤشر التحميل أولاً
            Navigator.of(context).pop();

            // عرض نافذة تفاصيل الخطأ مع معلومات إضافية
            final detailedError = '''
خطأ في استيراد الملف: ${file.name}

رسالة الخطأ:
$e

تفاصيل تقنية:
$stackTrace

معلومات الملف:
- اسم الملف: ${file.name}
- مسار الملف: ${kIsWeb ? 'ويب - غير متوفر' : (file.path ?? 'غير متوفر')}
- حجم الملف: ${file.size} بايت
            '''.trim();

            await _showDetailedErrorDialog(context, file.name, detailedError);

            // العودة مباشرة بعد عرض الخطأ - لا نكمل المعالجة
            return;
          }
        }

        // إغلاق مؤشر التحميل والعودة فقط إذا لم تكن هناك أخطاء
        if (!context.mounted) return;
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // إذا كان هناك نجاح، نعود للصفحة الرئيسية
        if (successCount > 0) {
          Navigator.of(context).pop(true); // العودة مع إشارة النجاح
        }

        // عرض نتيجة الاستيراد فقط في حالة النجاح
        if (successCount > 0) {
          final message = errorCount > 0
              ? 'تم استيراد $successCount محادثة بنجاح، فشل في $errorCount محادثة'
              : 'تم استيراد $successCount محادثة بنجاح';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        }
        // في حالة الخطأ، لا نعرض أي SnackBar لأن نافذة التفاصيل تظهر مباشرة
      } else {
        if (!context.mounted) return;
        // لا نعرض أي رسالة إذا لم يتم اختيار ملف - المستخدم ألغى العملية
      }
    } catch (e) {
      if (!context.mounted) return;

      final generalError = '''
❌ خطأ عام في عملية الاستيراد:
$e

⏰ وقت الخطأ: ${DateTime.now().toString()}

📱 معلومات التطبيق:
- الإصدار: v2.0.0+20
- النظام: ${Theme.of(context).platform}

💡 خطوات مقترحة للحل:
1. أعد تشغيل التطبيق
2. تأكد من صلاحيات الوصول للملفات
3. جرب ملف ZIP آخر
4. أرسل هذا التقرير للمطور
      '''.trim();

      // عرض نافذة التفاصيل مباشرة بدلاً من SnackBar
      await _showDetailedErrorDialog(context, 'عملية اختيار الملفات', generalError);
    }
  }







  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Color(0xFF075E54)),
            SizedBox(width: 8),
            Text('حول التطبيق'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('عارض واتساب', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Color(0xFF075E54))),
            SizedBox(height: 16),
            Text('تم تطوير هذا التطبيق بواسطة Ali Taha'),
            SizedBox(height: 16),
            Text('يدعم استيراد محادثات واتساب من ملفات ZIP', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }





  /// عرض نافذة تفاصيل الخطأ الشاملة
  Future<void> _showDetailedErrorDialog(BuildContext context, String fileName, String errorMessage) async {
    final now = DateTime.now();
    final timestamp = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    // إنشاء تقرير خطأ شامل
    final errorReport = '''
📁 اسم الملف: $fileName
⏰ وقت الخطأ: $timestamp

❌ رسالة الخطأ:
$errorMessage

📱 معلومات التطبيق:
- الإصدار: v2.0.0+20
- النظام: android

💡 خطوات مقترحة للحل:
1. تأكد من أن الملف مُصدّر من واتساب مباشرة
2. تحقق من وجود ملف .txt داخل ZIP
3. جرب تصدير المحادثة مرة أخرى من واتساب
4. أرسل هذا التقرير للمطور للمساعدة

---
تم إنشاء هذا التقرير من تطبيق عارض واتساب v2.0.0
    '''.trim();

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('تفاصيل الخطأ'),
          ],
        ),
        content: SingleChildScrollView(
          child: SelectableText(
            errorReport,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () async {
              await Clipboard.setData(ClipboardData(text: errorReport));
              // لا نعرض رسالة تأكيد النسخ - المستخدم سيعرف أنه تم النسخ
            },
            icon: const Icon(Icons.copy),
            label: const Text('نسخ'),
          ),
          TextButton.icon(
            onPressed: () async {
              await Share.share(errorReport);
            },
            icon: const Icon(Icons.share),
            label: const Text('مشاركة'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
