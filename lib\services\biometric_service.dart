import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الحماية بالبصمة/Face ID
class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();
  
  /// التحقق من توفر البصمة/Face ID
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من توفر البصمة: $e');
      return false;
    }
  }
  
  /// الحصول على أنواع البصمة المتاحة
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على أنواع البصمة: $e');
      return [];
    }
  }
  
  /// التحقق من البصمة/Face ID
  static Future<bool> authenticateWithBiometrics() async {
    try {
      final bool isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'يرجى التحقق من هويتك للوصول إلى التطبيق',
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );
      
      return isAuthenticated;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البصمة: $e');
      return false;
    }
  }
  
  /// تفعيل/إلغاء تفعيل الحماية بالبصمة
  static Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('biometric_enabled', enabled);
  }
  
  /// التحقق من تفعيل الحماية بالبصمة
  static Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('biometric_enabled') ?? false;
  }
  
  /// عرض نافذة إعدادات البصمة
  static Future<void> showBiometricSettings(BuildContext context) async {
    debugPrint('🔒 بدء عرض إعدادات البصمة');

    try {
      debugPrint('🔍 فحص توفر البصمة...');
      final isAvailable = await isBiometricAvailable();
      debugPrint('📱 البصمة متاحة: $isAvailable');

      debugPrint('🔍 فحص أنواع البصمة المتاحة...');
      final availableBiometrics = await getAvailableBiometrics();
      debugPrint('📋 أنواع البصمة: ${availableBiometrics.map((e) => e.toString()).join(', ')}');

      debugPrint('🔍 فحص حالة تفعيل البصمة...');
      bool isEnabled = await isBiometricEnabled();
      debugPrint('⚙️ البصمة مفعلة: $isEnabled');

      if (!context.mounted) {
        debugPrint('❌ Context غير متاح');
        return;
      }

      debugPrint('✅ عرض نافذة الإعدادات...');
    } catch (e) {
      debugPrint('❌ خطأ في تحضير إعدادات البصمة: $e');
      rethrow;
    }

    // إعادة تعريف المتغيرات للاستخدام في النافذة
    final isAvailable = await isBiometricAvailable();
    final availableBiometrics = await getAvailableBiometrics();
    bool isEnabled = await isBiometricEnabled();

    debugPrint('🔍 حالة البصمة:');
    debugPrint('   - متاحة: $isAvailable');
    debugPrint('   - مفعلة: $isEnabled');
    debugPrint('   - الأنواع المتاحة: $availableBiometrics');
    debugPrint('   - Switch سيكون ${isAvailable ? "مفعل" : "معطل"}');

    if (!context.mounted) {
      debugPrint('❌ Context غير متاح بعد العمليات async');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('🔒 الحماية بالبصمة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isAvailable) ...[
                const Text('❌ البصمة غير متاحة على هذا الجهاز'),
                const SizedBox(height: 16),
                const Text('يرجى التأكد من:'),
                const Text('• إعداد البصمة في إعدادات الجهاز'),
                const Text('• دعم الجهاز للبصمة'),
              ] else ...[
                const Text('✅ البصمة متاحة على هذا الجهاز'),
                const SizedBox(height: 16),
                const Text('الأنواع المتاحة:'),
                ...availableBiometrics.map((type) {
                  String typeName = '';
                  String icon = '';
                  switch (type) {
                    case BiometricType.face:
                      typeName = 'التعرف على الوجه';
                      icon = '👤';
                      break;
                    case BiometricType.fingerprint:
                      typeName = 'بصمة الإصبع';
                      icon = '👆';
                      break;
                    case BiometricType.iris:
                      typeName = 'مسح العين';
                      icon = '👁️';
                      break;
                    case BiometricType.strong:
                      typeName = 'حماية قوية';
                      icon = '🔐';
                      break;
                    case BiometricType.weak:
                      typeName = 'حماية ضعيفة';
                      icon = '🔓';
                      break;
                  }
                  return Text('$icon $typeName');
                }),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Text('تفعيل الحماية: '),
                    GestureDetector(
                      onTap: () {
                        debugPrint('🔄 تم الضغط على منطقة Switch');
                        if (!isAvailable) {
                          debugPrint('❌ Switch معطل - البصمة غير متاحة');
                        }
                      },
                      child: Switch(
                      value: isEnabled,
                      onChanged: isAvailable ? (value) async {
                        debugPrint('🔄 تم الضغط على زر التفعيل! القيمة الجديدة: $value');
                        debugPrint('🔄 تغيير حالة البصمة إلى: $value');
                        try {
                          if (value) {
                            debugPrint('🔒 محاولة تفعيل البصمة...');
                            // اختبار البصمة قبل التفعيل
                            final success = await authenticateWithBiometrics();
                            debugPrint('🔒 نتيجة اختبار البصمة: $success');

                            if (success) {
                              debugPrint('✅ نجح اختبار البصمة، حفظ الإعداد...');
                              await setBiometricEnabled(true);
                              setState(() {
                                isEnabled = true;
                              });
                              debugPrint('✅ تم تفعيل البصمة بنجاح');

                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('✅ تم تفعيل الحماية بالبصمة'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              }
                            } else {
                              debugPrint('❌ فشل اختبار البصمة');
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('❌ فشل في التحقق من البصمة'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          } else {
                            debugPrint('🔓 إلغاء تفعيل البصمة...');
                            await setBiometricEnabled(false);
                            setState(() {
                              isEnabled = false;
                            });
                            debugPrint('✅ تم إلغاء تفعيل البصمة');

                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('❌ تم إلغاء تفعيل الحماية بالبصمة'),
                                  backgroundColor: Colors.orange,
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          debugPrint('❌ خطأ في تغيير حالة البصمة: $e');
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('❌ خطأ: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      } : () {
                        debugPrint('❌ تم الضغط على Switch معطل - البصمة غير متاحة');
                        return null;
                      }(), // إذا كانت البصمة غير متاحة، يصبح Switch معطل
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        ),
      ),
    );
  }
  
  /// التحقق من البصمة عند بدء التطبيق
  static Future<bool> checkBiometricOnStartup() async {
    final isEnabled = await isBiometricEnabled();
    if (!isEnabled) return true;
    
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) return true;
    
    return await authenticateWithBiometrics();
  }
}
