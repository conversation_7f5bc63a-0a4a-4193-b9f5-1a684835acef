import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:video_player/video_player.dart';
// import 'package:cached_network_image/cached_network_image.dart'; // تم إزالته لأنه يحتاج الإنترنت

/// معرض الصور والفيديو المتقدم
class MediaGalleryWidget extends StatefulWidget {
  final List<String> mediaPaths;
  final int initialIndex;
  final List<String> mediaTypes;
  
  const MediaGalleryWidget({
    super.key,
    required this.mediaPaths,
    required this.initialIndex,
    required this.mediaTypes,
  });

  @override
  State<MediaGalleryWidget> createState() => _MediaGalleryWidgetState();
}

class _MediaGalleryWidgetState extends State<MediaGalleryWidget> {
  late PageController _pageController;
  int _currentIndex = 0;
  
  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          '${_currentIndex + 1} من ${widget.mediaPaths.length}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // مشاركة الوسائط
              _shareCurrentMedia();
            },
          ),
        ],
      ),
      body: PhotoViewGallery.builder(
        pageController: _pageController,
        itemCount: widget.mediaPaths.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        builder: (context, index) {
          final mediaPath = widget.mediaPaths[index];
          final mediaType = widget.mediaTypes[index];
          
          if (mediaType == 'video') {
            return PhotoViewGalleryPageOptions.customChild(
              child: VideoPlayerWidget(videoPath: mediaPath),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
            );
          } else {
            return PhotoViewGalleryPageOptions(
              imageProvider: FileImage(File(mediaPath)),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 3,
              heroAttributes: PhotoViewHeroAttributes(tag: 'media_$index'),
            );
          }
        },
        scrollPhysics: const BouncingScrollPhysics(),
        backgroundDecoration: const BoxDecoration(color: Colors.black),
      ),
    );
  }
  
  void _shareCurrentMedia() {
    // تنفيذ مشاركة الوسائط
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔄 جاري تطوير ميزة المشاركة...'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

/// مشغل الفيديو المدمج
class VideoPlayerWidget extends StatefulWidget {
  final String videoPath;
  
  const VideoPlayerWidget({
    super.key,
    required this.videoPath,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> with TickerProviderStateMixin {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _showControls = true;
  bool _isDragging = false;
  bool _isDisposed = false; // فلاج لمنع العمليات بعد التنظيف
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;
  Timer? _hideControlsTimer;

  // إدارة متقدمة للذاكرة
  static final Set<String> _activeVideos = <String>{}; // تتبع الفيديوهات النشطة
  static const int _maxActiveVideos = 3; // حد أقصى للفيديوهات النشطة

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      debugPrint('🎬 بدء تهيئة الفيديو: ${widget.videoPath}');

      // التحقق من أن الـ widget ما زال mounted
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ تم إلغاء تهيئة الفيديو - Widget غير mounted أو تم تنظيفه');
        return;
      }

      // إدارة الذاكرة - تنظيف الفيديوهات القديمة إذا تجاوزنا الحد الأقصى
      if (_activeVideos.length >= _maxActiveVideos) {
        debugPrint('🧹 تنظيف الفيديوهات القديمة - العدد الحالي: ${_activeVideos.length}');
        _cleanupOldVideos();
      }

      // إضافة الفيديو الحالي للقائمة النشطة
      _activeVideos.add(widget.videoPath);

      // التحقق من وجود الملف أولاً
      final file = File(widget.videoPath);
      if (!await file.exists()) {
        debugPrint('❌ ملف الفيديو غير موجود: ${widget.videoPath}');
        if (mounted) {
          setState(() {
            _isInitialized = false;
          });
          _showVideoError('الملف غير موجود');
        }
        return;
      }

      // فحص حجم الملف
      final fileSize = await file.length();
      debugPrint('📊 حجم ملف الفيديو: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB');

      if (fileSize == 0) {
        debugPrint('❌ ملف الفيديو فارغ');
        if (mounted) {
          _showVideoError('ملف الفيديو فارغ أو تالف');
        }
        return;
      }

      debugPrint('✅ ملف الفيديو موجود، بدء التهيئة...');
      _controller = VideoPlayerController.file(file);

      // إعطاء وقت أكثر للتهيئة مع timeout أطول للفيديوهات الكبيرة
      await _controller.initialize().timeout(
        const Duration(seconds: 30), // زيادة الوقت لـ 30 ثانية
        onTimeout: () {
          debugPrint('⏰ انتهت مهلة تهيئة الفيديو بعد 30 ثانية');
          throw Exception('انتهت مهلة تحميل الفيديو - الملف كبير جداً أو تالف');
        },
      );

      if (mounted) {
        debugPrint('✅ تم تهيئة الفيديو بنجاح');
        debugPrint('📊 معلومات الفيديو:');
        debugPrint('   - المدة: ${_controller.value.duration}');
        debugPrint('   - الأبعاد: ${_controller.value.size}');
        debugPrint('   - نسبة العرض للارتفاع: ${_controller.value.aspectRatio}');

        setState(() {
          _isInitialized = true;
        });

        // التأكد من أن الفيديو يبدأ من البداية وتشغيله مباشرة
        await _controller.seekTo(Duration.zero);
        await _controller.play(); // تشغيل مباشر
        debugPrint('✅ الفيديو يعمل الآن');

        // إظهار التحكم في البداية
        _controlsAnimationController.forward();

        // إخفاء التحكم بعد 5 ثوان (وقت أطول للمستخدم)
        _hideControlsAfterDelay();

        // الاستماع لتغييرات الفيديو
        _controller.addListener(_videoListener);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفيديو: $e');
      debugPrint('❌ تفاصيل الخطأ: ${e.toString()}');

      if (mounted) {
        setState(() {
          _isInitialized = false;
        });

        // تحديد نوع الخطأ وإظهار رسالة مناسبة
        String errorMessage = 'خطأ غير معروف';
        if (e.toString().contains('MediaCodec')) {
          errorMessage = 'تنسيق الفيديو غير مدعوم أو الملف تالف';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'انتهت مهلة تحميل الفيديو - الملف قد يكون كبيراً جداً';
        } else if (e.toString().contains('VideoError')) {
          errorMessage = 'خطأ في مشغل الفيديو - جرب إعادة فتح الملف';
        }

        _showVideoError(errorMessage);
      }
    }
  }

  void _showVideoError(String message) {
    if (!mounted) return;

    // إنشاء تفاصيل تشخيصية
    final fileName = widget.videoPath.split('/').last;
    final fileExtension = fileName.split('.').last.toLowerCase();

    String diagnosticInfo = '';
    if (fileExtension == 'mp4') {
      diagnosticInfo = 'ملف MP4 - قد يحتوي على ترميز غير مدعوم';
    } else if (fileExtension == 'avi') {
      diagnosticInfo = 'ملف AVI - قد يحتاج تحويل لـ MP4';
    } else if (fileExtension == 'mov') {
      diagnosticInfo = 'ملف MOV - قد يحتاج تحويل لـ MP4';
    } else if (fileExtension == 'mkv') {
      diagnosticInfo = 'ملف MKV - غير مدعوم، يحتاج تحويل لـ MP4';
    } else {
      diagnosticInfo = 'تنسيق غير معروف - جرب تحويل الملف لـ MP4';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('❌ خطأ في تحميل الفيديو:',
                 style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text(message),
            const SizedBox(height: 4),
            Text('الملف: $fileName',
                 style: const TextStyle(fontSize: 12, color: Colors.white70)),
            const SizedBox(height: 2),
            Text(diagnosticInfo,
                 style: const TextStyle(fontSize: 11, color: Colors.white60)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 10),
        action: SnackBarAction(
          label: 'نسخ التفاصيل',
          textColor: Colors.white,
          onPressed: () async {
            // نسخ تفاصيل الخطأ للحافظة
            final errorDetails = '''
❌ خطأ في الفيديو:
$message

📁 الملف: $fileName
📂 المسار: ${widget.videoPath}
🔍 التشخيص: $diagnosticInfo
⏰ الوقت: ${DateTime.now().toString()}
📱 التطبيق: واتساب البسيط
            ''';

            try {
              await Clipboard.setData(ClipboardData(text: errorDetails));
              debugPrint('✅ تم نسخ تفاصيل الخطأ للحافظة');

              if (mounted) {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('📋 تم نسخ تفاصيل الخطأ للحافظة'),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            } catch (e) {
              debugPrint('❌ فشل في نسخ التفاصيل: $e');
              if (mounted) {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('❌ فشل في نسخ التفاصيل'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            }
          },
        ),
      ),
    );
  }

  void _videoListener() {
    if (!mounted || _isDisposed) return;

    try {
      // فحص الأخطاء أولاً
      if (_controller.value.hasError) {
        debugPrint('❌ خطأ في الفيديو: ${_controller.value.errorDescription}');

        // إظهار رسالة خطأ مفصلة
        String errorMsg = _controller.value.errorDescription ?? 'خطأ غير معروف';
        if (errorMsg.contains('MediaCodec')) {
          errorMsg = 'تنسيق الفيديو غير مدعوم - جرب تحويل الملف لتنسيق MP4';
        } else if (errorMsg.contains('decoder')) {
          errorMsg = 'فشل في فك تشفير الفيديو - الملف قد يكون تالفاً';
        }

        _showVideoError('خطأ أثناء التشغيل: $errorMsg');
        return;
      }

      // تحديث واجهة المستخدم فقط إذا لم يكن المستخدم يسحب شريط التقدم
      if (!_isDragging && mounted && !_isDisposed) {
        setState(() {});
      }

      // إخفاء التحكم عند انتهاء الفيديو
      if (_controller.value.position >= _controller.value.duration) {
        _controlsAnimationController.forward();
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في مستمع الفيديو: $e');
    }
  }

  void _hideControlsAfterDelay() {
    // إلغاء المؤقت السابق إن وجد
    _hideControlsTimer?.cancel();

    // التحقق من حالة التنظيف
    if (_isDisposed || !mounted) {
      return;
    }

    // إنشاء مؤقت جديد
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && !_isDisposed && _controller != null && _controller!.value.isPlaying && !_isDragging) {
        try {
          _controlsAnimationController.reverse();
          if (mounted && !_isDisposed) {
            setState(() {
              _showControls = false;
            });
          }
        } catch (e) {
          debugPrint('⚠️ خطأ في إخفاء الأزرار: $e');
        }
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _controlsAnimationController.forward();
      _hideControlsAfterDelay();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  /// تنظيف الفيديوهات القديمة من الذاكرة
  static void _cleanupOldVideos() {
    try {
      // إجبار تنظيف الذاكرة
      debugPrint('🧹 بدء تنظيف الذاكرة...');

      // تنظيف قائمة الفيديوهات النشطة (الاحتفاظ بآخر فيديو فقط)
      if (_activeVideos.length > 1) {
        final lastVideo = _activeVideos.last;
        _activeVideos.clear();
        _activeVideos.add(lastVideo);
      }

      // إجبار تنظيف الذاكرة من النظام
      debugPrint('🗑️ طلب تنظيف الذاكرة من النظام...');

    } catch (e) {
      debugPrint('⚠️ خطأ في تنظيف الفيديوهات القديمة: $e');
    }
  }

  @override
  void dispose() {
    debugPrint('🗑️ تنظيف مشغل الفيديو: ${widget.videoPath}');

    // تعيين فلاج التنظيف
    _isDisposed = true;

    // إزالة الفيديو من القائمة النشطة
    _activeVideos.remove(widget.videoPath);

    // إيقاف جميع العمليات فوراً
    _hideControlsTimer?.cancel();

    // تنظيف المتحكم بالرسوم المتحركة
    try {
      if (_controlsAnimationController.isAnimating) {
        _controlsAnimationController.stop();
      }
      _controlsAnimationController.dispose();
    } catch (e) {
      debugPrint('⚠️ خطأ في تنظيف الرسوم المتحركة: $e');
    }

    // تنظيف مشغل الفيديو بأمان
    try {
      _controller.removeListener(_videoListener);
      if (_controller.value.isInitialized) {
        _controller.pause(); // إيقاف التشغيل أولاً
        // انتظار قصير للتأكد من الإيقاف
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!_isDisposed) return; // تأكد من أننا ما زلنا في حالة التنظيف
          try {
            _controller.dispose(); // ثم التنظيف
          } catch (e) {
            debugPrint('⚠️ خطأ في تنظيف المشغل المتأخر: $e');
          }
        });
      } else {
        _controller.dispose(); // تنظيف مباشر إذا لم يكن مهيأ
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في تنظيف مشغل الفيديو: $e');
    }

    debugPrint('✅ تم تنظيف مشغل الفيديو بنجاح');
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  Widget _buildVideoControls() {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    final position = _controller.value.position;
    final duration = _controller.value.duration;
    final progress = duration.inMilliseconds > 0
        ? position.inMilliseconds / duration.inMilliseconds
        : 0.0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // شريط التقدم
          Row(
            children: [
              // الوقت الحالي
              Text(
                _formatDuration(position),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(width: 12),

              // شريط التقدم
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 3,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                    activeTrackColor: const Color(0xFF25D366), // أخضر واتساب
                    inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                    thumbColor: const Color(0xFF25D366),
                    overlayColor: const Color(0xFF25D366).withValues(alpha: 0.2),
                  ),
                  child: Directionality(
                    textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                    child: Slider(
                      value: progress.clamp(0.0, 1.0),
                      onChangeStart: (value) {
                        setState(() {
                          _isDragging = true;
                        });
                      },
                      onChanged: (value) {
                        final newPosition = Duration(
                          milliseconds: (value * duration.inMilliseconds).round(),
                        );
                        _controller.seekTo(newPosition);
                      },
                      onChangeEnd: (value) {
                        setState(() {
                          _isDragging = false;
                        });
                        // إعطاء وقت أطول قبل إخفاء التحكم بعد السحب
                        Future.delayed(const Duration(seconds: 5), () {
                          if (mounted && _controller.value.isPlaying && !_isDragging) {
                            _controlsAnimationController.reverse();
                            setState(() {
                              _showControls = false;
                            });
                          }
                        });
                      },
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // المدة الكاملة
              Text(
                _formatDuration(duration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      // إذا كان هناك خطأ في التهيئة، اعرض شاشة خطأ
      if (_controller.value.hasError) {
        return Container(
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'فشل في تحميل الفيديو',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'الملف قد يكون تالفاً أو بتنسيق غير مدعوم',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'الملف: ${widget.videoPath.split('/').last}',
                  style: const TextStyle(
                    color: Colors.white54,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('العودة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // شاشة التحميل العادية
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
              const SizedBox(height: 16),
              const Text(
                'جاري تحميل الفيديو...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'الملف: ${widget.videoPath.split('/').last}',
                style: const TextStyle(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    
    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        alignment: Alignment.center,
        children: [
          AspectRatio(
            aspectRatio: _controller.value.aspectRatio,
            child: VideoPlayer(_controller),
          ),

          // شريط التحكم المحسن
          AnimatedBuilder(
            animation: _controlsAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _controlsAnimation.value,
                child: Container(
                  color: Colors.black.withValues(alpha: 0.4 * _controlsAnimation.value),
                  child: Stack(
                    children: [
                      // زر التشغيل/الإيقاف في المنتصف
                      Center(
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black.withValues(alpha: 0.6),
                          ),
                          child: IconButton(
                            icon: Icon(
                              _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                              size: 40,
                            ),
                            onPressed: () async {
                              try {
                                debugPrint('🎮 تم الضغط على زر التشغيل/الإيقاف');
                                debugPrint('📊 حالة الفيديو الحالية: ${_controller.value.isPlaying ? "يعمل" : "متوقف"}');

                                if (_controller.value.isPlaying) {
                                  await _controller.pause();
                                  debugPrint('⏸️ تم إيقاف الفيديو');
                                } else {
                                  await _controller.play();
                                  debugPrint('▶️ تم تشغيل الفيديو');
                                }

                                if (mounted) {
                                  setState(() {});
                                }
                              } catch (e) {
                                debugPrint('❌ خطأ في تشغيل/إيقاف الفيديو: $e');
                                // إزالة SnackBar لتجنب مشاكل BuildContext
                              }
                              _hideControlsAfterDelay();
                            },
                          ),
                        ),
                      ),

                      // شريط التحكم السفلي
                      Positioned(
                        bottom: 80, // رفع الشريط أكثر بكثير مثل واتساب
                        left: 16,
                        right: 16,
                        child: _buildVideoControls(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// عنصر صورة مصغرة للفقاعة
class ThumbnailWidget extends StatelessWidget {
  final String imagePath;
  final double size;
  final VoidCallback? onTap;
  
  const ThumbnailWidget({
    super.key,
    required this.imagePath,
    this.size = 120,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('🖼️ ThumbnailWidget: عرض صورة من المسار: $imagePath');
    debugPrint('🖼️ ThumbnailWidget: الملف موجود: ${File(imagePath).existsSync()}');

    return GestureDetector(
      onTap: () {
        debugPrint('🖼️ ThumbnailWidget: تم الضغط على الصورة');
        if (onTap != null) {
          onTap!();
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: size,
          height: size,
          child: File(imagePath).existsSync()
              ? Image.file(
                  File(imagePath),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    debugPrint('❌ ThumbnailWidget: خطأ في تحميل الصورة: $error');
                    return Container(
                      color: Colors.grey[300],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.broken_image,
                            color: Colors.red,
                            size: 32,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'خطأ في الصورة',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                )
              : Container(
                  color: Colors.grey[300],
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.image_not_supported,
                        color: Colors.orange,
                        size: 32,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ملف غير موجود',
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}

/// عنصر فيديو مصغر للفقاعة
class VideoThumbnailWidget extends StatelessWidget {
  final String videoPath;
  final double size;
  final VoidCallback? onTap;
  
  const VideoThumbnailWidget({
    super.key,
    required this.videoPath,
    this.size = 120,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('🎥 VideoThumbnailWidget: عرض فيديو من المسار: $videoPath');
    debugPrint('🎥 VideoThumbnailWidget: الملف موجود: ${File(videoPath).existsSync()}');

    return GestureDetector(
      onTap: () {
        debugPrint('🎥 VideoThumbnailWidget: تم الضغط على الفيديو');
        if (onTap != null) {
          onTap!();
        }
      },
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[300],
          border: Border.all(color: Colors.grey[400]!, width: 1),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: File(videoPath).existsSync() ? Colors.black : Colors.grey[300],
                child: File(videoPath).existsSync()
                    ? const Icon(
                        Icons.videocam,
                        color: Colors.white,
                        size: 48,
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.video_file,
                            color: Colors.orange,
                            size: 32,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'ملف غير موجود',
                            style: TextStyle(
                              color: Colors.orange,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
              ),
            ),

            // أيقونة التشغيل
            if (File(videoPath).existsSync())
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.6),
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 32,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
