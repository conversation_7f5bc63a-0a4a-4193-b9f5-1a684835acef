import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';

/// شاشة المفضلة المنفصلة
class FavoritesScreen extends StatefulWidget {
  final String chatId;
  final String chatName;
  final List<ChatMessage> messages;
  final Function(ChatMessage)? onMessageTap;

  const FavoritesScreen({
    super.key,
    required this.chatId,
    required this.chatName,
    required this.messages,
    this.onMessageTap,
  });

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  List<ChatMessage> favorites = [];
  Set<String> _favoriteMessageIds = {};
  bool _isLoading = true;
  String _debugInfo = ''; // معلومات التشخيص المرئية

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  /// إنشاء معرف فريد للرسالة (نفس الطريقة المستخدمة في chat_screen)
  String _createMessageId(ChatMessage message) {
    return '${message.timestamp.millisecondsSinceEpoch}_${message.sender.hashCode}_${message.content.hashCode}';
  }

  /// تحميل المفضلة من SharedPreferences
  Future<void> _loadFavorites() async {
    debugPrint('🚀 بدء تحميل المفضلة...');
    debugPrint('📱 معرف المحادثة: ${widget.chatId}');
    debugPrint('💬 اسم المحادثة: ${widget.chatName}');
    debugPrint('📝 عدد الرسائل الكلي: ${widget.messages.length}');

    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chatId}';
    final favoritesList = prefs.getStringList(favoritesKey) ?? [];

    debugPrint('🔍 البحث عن المفضلة بالمفتاح: $favoritesKey');
    debugPrint('📋 قائمة المفضلة المحفوظة: $favoritesList');
    debugPrint('🔢 عدد المعرفات المحفوظة: ${favoritesList.length}');

    setState(() {
      _favoriteMessageIds = favoritesList.toSet();

      // بناء معلومات التشخيص المرئية الشاملة
      _debugInfo = '''
🔍 تشخيص المفضلة الشامل:
📱 معرف المحادثة: ${widget.chatId}
💬 اسم المحادثة: ${widget.chatName}
📝 عدد الرسائل الكلي: ${widget.messages.length}
🔑 مفتاح البحث: $favoritesKey
📋 معرفات محفوظة: ${favoritesList.length}

🗂️ جميع المعرفات المحفوظة:
${favoritesList.isEmpty ? '   (لا توجد معرفات محفوظة)' : favoritesList.map((id) => '   - $id').join('\n')}

🔧 معلومات تقنية:
   - نوع البيانات: ${favoritesList.runtimeType}
   - حالة التحميل: ${_isLoading ? 'جاري التحميل' : 'مكتمل'}
   - حالة المتغيرات: ${_favoriteMessageIds.length} معرف في الذاكرة
''';

      // تصفية الرسائل المفضلة
      favorites.clear();
      int matchCount = 0;
      List<String> sampleMessageIds = [];

      for (int i = 0; i < widget.messages.length && sampleMessageIds.length < 3; i++) {
        final message = widget.messages[i];
        final messageId = _createMessageId(message);
        sampleMessageIds.add(messageId);

        debugPrint('🔍 فحص رسالة: $messageId');
        debugPrint('   المحتوى: ${message.content.length > 50 ? message.content.substring(0, 50) + '...' : message.content}');

        if (_favoriteMessageIds.contains(messageId)) {
          favorites.add(message);
          matchCount++;
          debugPrint('✅ رسالة مفضلة موجودة: ${message.content.substring(0, 30)}...');
        }
      }

      // فحص باقي الرسائل بدون طباعة تفاصيل
      for (int i = 3; i < widget.messages.length; i++) {
        final message = widget.messages[i];
        final messageId = _createMessageId(message);

        if (_favoriteMessageIds.contains(messageId)) {
          favorites.add(message);
          matchCount++;
        }
      }

      // إضافة نتائج المطابقة والتحليل الشامل للتشخيص
      _debugInfo += '''

📊 نتائج التحليل:
   - المطابقات الموجودة: $matchCount من ${favoritesList.length}
   - رسائل محملة في الشاشة: ${favorites.length}
   - حالة القائمة: ${favorites.isEmpty ? 'فارغة' : 'تحتوي على رسائل'}

🔍 عينة من معرفات الرسائل الحالية (أول 5):
${sampleMessageIds.isEmpty ? '   (لا توجد رسائل)' : sampleMessageIds.map((id) => '   - $id').join('\n')}

🔄 مقارنة المعرفات:
${favoritesList.isNotEmpty && sampleMessageIds.isNotEmpty ?
  (favoritesList.any((saved) => sampleMessageIds.contains(saved)) ?
    '✅ يوجد تطابق بين المعرفات' :
    '❌ لا يوجد تطابق - المعرفات مختلفة تماماً') :
  '⚠️ لا توجد بيانات كافية للمقارنة'}

🧪 تحليل المشكلة:
${matchCount == 0 && favoritesList.isNotEmpty ?
  '🚨 المشكلة: المعرفات محفوظة لكن لا تتطابق مع الرسائل الحالية' :
  matchCount > 0 ?
    '✅ المعرفات تتطابق - المشكلة في مكان آخر' :
    '⚠️ لا توجد معرفات محفوظة أصلاً'}

🔧 معلومات إضافية:
   - SharedPreferences Key: $favoritesKey
   - Widget Messages Count: ${widget.messages.length}
   - Favorites Set Size: ${_favoriteMessageIds.length}
   - Sample Message Types: ${widget.messages.take(3).map((m) => m.type).join(', ')}
   - Sample Senders: ${widget.messages.take(3).map((m) => m.sender).toSet().join(', ')}
''';

      _isLoading = false;
      debugPrint('📊 نتائج المطابقة: $matchCount من ${favoritesList.length}');
    });

    debugPrint('⭐ تم تحميل ${favorites.length} رسالة مفضلة');
  }

  /// إزالة رسالة من المفضلة
  Future<void> _removeFavorite(ChatMessage message) async {
    final messageId = _createMessageId(message);

    setState(() {
      _favoriteMessageIds.remove(messageId);
      favorites.removeWhere((msg) => _createMessageId(msg) == messageId);
    });
    
    // حفظ التغييرات
    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chatId}';
    await prefs.setStringList(favoritesKey, _favoriteMessageIds.toList());
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🗑️ تم إزالة الرسالة من المفضلة'),
          backgroundColor: Color(0xFF25D366),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// تنسيق الوقت للعرض
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  /// إظهار خيارات الرسالة المفضلة
  void _showFavoriteOptions(ChatMessage message) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2A2F32),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.launch, color: Colors.blue),
                title: const Text('الانتقال للرسالة', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.of(context).pop();
                  if (widget.onMessageTap != null) {
                    Navigator.of(context).pop(); // إغلاق شاشة المفضلة
                    widget.onMessageTap!(message);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.star_border, color: Colors.red),
                title: const Text('إزالة من المفضلة', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.of(context).pop();
                  _removeFavorite(message);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0B141A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2F32),
        title: Text(
          '⭐ المفضلة - ${widget.chatName}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر التشخيص المرئي
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white),
            onPressed: () {
              _showDebugInfo();
            },
            tooltip: 'معلومات التشخيص',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF25D366),
              ),
            )
          : favorites.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.star_border,
                        size: 64,
                        color: Colors.white54,
                      ),
                      SizedBox(height: 16),
                      Text(
                        '⭐ لا توجد رسائل مفضلة',
                        style: TextStyle(color: Colors.white70, fontSize: 18),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط مطولاً على أي رسالة في الدردشة لإضافتها للمفضلة',
                        style: TextStyle(color: Colors.white54, fontSize: 14),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: favorites.length,
                  itemBuilder: (context, index) {
                    final message = favorites[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      color: const Color(0xFF2A2F32),
                      child: ListTile(
                        leading: const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 24,
                        ),
                        title: Text(
                          message.content.length > 100 
                            ? '${message.content.substring(0, 100)}...'
                            : message.content,
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          '${message.sender} • ${_formatTimestamp(message.timestamp)}',
                          style: const TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white54,
                          size: 16,
                        ),
                        onTap: () {
                          // الانتقال للرسالة الأصلية في الدردشة
                          if (widget.onMessageTap != null) {
                            Navigator.of(context).pop(); // إغلاق شاشة المفضلة
                            widget.onMessageTap!(message); // استدعاء callback للانتقال
                          }
                        },
                        onLongPress: () {
                          // إظهار خيارات إضافية
                          _showFavoriteOptions(message);
                        },
                      ),
                    );
                  },
                ),
    );
  }

  /// عرض معلومات التشخيص
  void _showDebugInfo() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2A2F32),
          title: const Text(
            '🔍 معلومات التشخيص',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Text(
              _debugInfo,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'إغلاق',
                style: TextStyle(color: Color(0xFF25D366)),
              ),
            ),
            TextButton(
              onPressed: () {
                // نسخ معلومات التشخيص للحافظة
                Clipboard.setData(ClipboardData(text: _debugInfo));
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('📋 تم نسخ معلومات التشخيص'),
                    backgroundColor: Color(0xFF25D366),
                  ),
                );
              },
              child: const Text(
                'نسخ',
                style: TextStyle(color: Color(0xFF25D366)),
              ),
            ),
          ],
        );
      },
    );
  }
}
