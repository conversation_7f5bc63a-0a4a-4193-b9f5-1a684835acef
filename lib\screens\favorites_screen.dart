import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';

/// شاشة المفضلة المنفصلة
class FavoritesScreen extends StatefulWidget {
  final String chatId;
  final String chatName;
  final List<ChatMessage> messages;
  final Function(ChatMessage)? onMessageTap;

  const FavoritesScreen({
    super.key,
    required this.chatId,
    required this.chatName,
    required this.messages,
    this.onMessageTap,
  });

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  List<ChatMessage> favorites = [];
  Set<String> _favoriteMessageIds = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  /// تحميل المفضلة من SharedPreferences
  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chatId}';
    final favoritesList = prefs.getStringList(favoritesKey) ?? [];
    
    debugPrint('🔍 البحث عن المفضلة بالمفتاح: $favoritesKey');
    debugPrint('📋 قائمة المفضلة المحفوظة: $favoritesList');
    
    setState(() {
      _favoriteMessageIds = favoritesList.toSet();
      
      // تصفية الرسائل المفضلة
      favorites.clear();
      for (final message in widget.messages) {
        final messageId = '${message.timestamp.millisecondsSinceEpoch}_${message.sender}_${message.content.hashCode}';
        if (_favoriteMessageIds.contains(messageId)) {
          favorites.add(message);
          debugPrint('⭐ رسالة مفضلة: ${message.content.substring(0, 30)}...');
        }
      }
      
      _isLoading = false;
    });
    
    debugPrint('⭐ تم تحميل ${favorites.length} رسالة مفضلة');
  }

  /// إزالة رسالة من المفضلة
  Future<void> _removeFavorite(ChatMessage message) async {
    final messageId = '${message.timestamp.millisecondsSinceEpoch}_${message.sender}_${message.content.hashCode}';
    
    setState(() {
      _favoriteMessageIds.remove(messageId);
      favorites.removeWhere((msg) => 
        '${msg.timestamp.millisecondsSinceEpoch}_${msg.sender}_${msg.content.hashCode}' == messageId
      );
    });
    
    // حفظ التغييرات
    final prefs = await SharedPreferences.getInstance();
    final favoritesKey = 'chat_favorites_${widget.chatId}';
    await prefs.setStringList(favoritesKey, _favoriteMessageIds.toList());
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🗑️ تم إزالة الرسالة من المفضلة'),
          backgroundColor: Color(0xFF25D366),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// تنسيق الوقت للعرض
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  /// إظهار خيارات الرسالة المفضلة
  void _showFavoriteOptions(ChatMessage message) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2A2F32),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.launch, color: Colors.blue),
                title: const Text('الانتقال للرسالة', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.of(context).pop();
                  if (widget.onMessageTap != null) {
                    Navigator.of(context).pop(); // إغلاق شاشة المفضلة
                    widget.onMessageTap!(message);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.star_border, color: Colors.red),
                title: const Text('إزالة من المفضلة', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.of(context).pop();
                  _removeFavorite(message);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0B141A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2F32),
        title: Text(
          '⭐ المفضلة - ${widget.chatName}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF25D366),
              ),
            )
          : favorites.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.star_border,
                        size: 64,
                        color: Colors.white54,
                      ),
                      SizedBox(height: 16),
                      Text(
                        '⭐ لا توجد رسائل مفضلة',
                        style: TextStyle(color: Colors.white70, fontSize: 18),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط مطولاً على أي رسالة في الدردشة لإضافتها للمفضلة',
                        style: TextStyle(color: Colors.white54, fontSize: 14),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: favorites.length,
                  itemBuilder: (context, index) {
                    final message = favorites[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      color: const Color(0xFF2A2F32),
                      child: ListTile(
                        leading: const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 24,
                        ),
                        title: Text(
                          message.content.length > 100 
                            ? '${message.content.substring(0, 100)}...'
                            : message.content,
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          '${message.sender} • ${_formatTimestamp(message.timestamp)}',
                          style: const TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white54,
                          size: 16,
                        ),
                        onTap: () {
                          // الانتقال للرسالة الأصلية في الدردشة
                          if (widget.onMessageTap != null) {
                            Navigator.of(context).pop(); // إغلاق شاشة المفضلة
                            widget.onMessageTap!(message); // استدعاء callback للانتقال
                          }
                        },
                        onLongPress: () {
                          // إظهار خيارات إضافية
                          _showFavoriteOptions(message);
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
